# Environment Variables Template
# Copy this file to .env and fill in your actual values

# API Keys for Data Sources
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
TWELVEDATA_API_KEY=your_twelvedata_api_key_here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Database Configuration (if using PostgreSQL)
DATABASE_URL=sqlite:///data/forex_ai.db
# DATABASE_URL=postgresql://username:password@localhost:5432/forex_ai

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379/0

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# Security
SECRET_KEY=your_secret_key_for_jwt_tokens_here

# Environment
ENVIRONMENT=development  # development, staging, production

# Logging
LOG_LEVEL=INFO

# Model Configuration
MODEL_PATH=models/
DATA_PATH=data/

# Web Dashboard
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8000

# Trading Configuration
DEMO_MODE=true  # Set to false for live trading
MAX_POSITION_SIZE=1000  # Maximum position size in USD
