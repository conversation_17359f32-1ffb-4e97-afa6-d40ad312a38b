"""
Simple configuration management for Forex AI System
"""

import os
import yaml
from typing import Any, Optional
from pathlib import Path


class SimpleConfig:
    """Simple configuration class"""
    
    def __init__(self):
        # Data sources
        self.data_sources_primary = "yfinance"
        self.data_sources_secondary = "alpha_vantage"
        self.data_sources_backup = "twelvedata"
        self.alpha_vantage_key = None
        self.twelvedata_key = None
        
        # Trading
        self.trading_symbol = "XAUUSD"
        self.trading_timeframes = ["15m", "1h", "4h"]
        self.historical_data_years = 5
        self.signals_min_confidence = 0.75
        self.signals_max_signals_per_day = 10
        self.signals_risk_reward_ratio = 3.0
        self.signals_stop_loss_pips = 30
        self.signals_take_profit_pips = 90
        
        # AI Models
        self.ai_models_primary_model = "transformer"
        self.ai_models_ensemble_models = ["transformer", "cnn_lstm"]
        self.transformer_sequence_length = 100
        self.transformer_d_model = 256
        self.transformer_nhead = 8
        self.transformer_num_layers = 6
        self.transformer_dropout = 0.1
        
        # Database
        self.database_type = "sqlite"
        self.database_path = "data/forex_ai.db"
        
        # Telegram
        self.telegram_enabled = True
        self.telegram_bot_token = None
        self.telegram_chat_id = None
        
        # Dashboard
        self.dashboard_enabled = True
        self.dashboard_host = "0.0.0.0"
        self.dashboard_port = 8000
        
        # Logging
        self.logging_level = "INFO"
        self.logging_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
        self.logging_main_file = "logs/forex_ai.log"
        self.logging_error_file = "logs/errors.log"
        
        # Features
        self.technical_indicators = [
            "ema_20", "ema_50", "ema_200", "sma_20", "sma_50",
            "rsi_14", "macd", "stoch", "williams_r",
            "atr_14", "bollinger_bands", "keltner_channels",
            "volume_sma", "volume_ratio",
            "higher_highs", "lower_lows", "support_resistance"
        ]
        self.candlestick_patterns = [
            "doji", "hammer", "engulfing", "shooting_star", "morning_star", "evening_star"
        ]


# Create compatibility classes for the existing code
class DataSourceConfig:
    def __init__(self, config):
        self.primary = config.data_sources_primary
        self.secondary = config.data_sources_secondary
        self.backup = config.data_sources_backup
        self.alpha_vantage_key = config.alpha_vantage_key
        self.twelvedata_key = config.twelvedata_key


class HistoricalData:
    def __init__(self, config):
        self.years = config.historical_data_years


class Signals:
    def __init__(self, config):
        self.min_confidence = config.signals_min_confidence
        self.max_signals_per_day = config.signals_max_signals_per_day
        self.risk_reward_ratio = config.signals_risk_reward_ratio
        self.stop_loss_pips = config.signals_stop_loss_pips
        self.take_profit_pips = config.signals_take_profit_pips


class TradingConfig:
    def __init__(self, config):
        self.symbol = config.trading_symbol
        self.timeframes = config.trading_timeframes
        self.historical_data = HistoricalData(config)
        self.signals = Signals(config)


class TransformerConfig:
    def __init__(self, config):
        self.sequence_length = config.transformer_sequence_length
        self.d_model = config.transformer_d_model
        self.nhead = config.transformer_nhead
        self.num_layers = config.transformer_num_layers
        self.dropout = config.transformer_dropout


class CNNLSTMConfig:
    def __init__(self, config):
        self.cnn_filters = [32, 64, 128]
        self.lstm_units = 128
        self.dropout = 0.2


class TrainingConfig:
    def __init__(self, config):
        self.batch_size = 32
        self.epochs = 100
        self.learning_rate = 0.001
        self.validation_split = 0.2
        self.early_stopping_patience = 10


class AIModelsConfig:
    def __init__(self, config):
        self.primary_model = config.ai_models_primary_model
        self.ensemble_models = config.ai_models_ensemble_models
        self.transformer = TransformerConfig(config)
        self.cnn_lstm = CNNLSTMConfig(config)
        self.training = TrainingConfig(config)


class FeaturesConfig:
    def __init__(self, config):
        self.technical_indicators = config.technical_indicators
        self.candlestick_patterns = config.candlestick_patterns


class DatabaseConfig:
    def __init__(self, config):
        self.type = config.database_type
        self.path = config.database_path


class TelegramConfig:
    def __init__(self, config):
        self.enabled = config.telegram_enabled
        self.bot_token = config.telegram_bot_token
        self.chat_id = config.telegram_chat_id


class DashboardConfig:
    def __init__(self, config):
        self.enabled = config.dashboard_enabled
        self.host = config.dashboard_host
        self.port = config.dashboard_port


class LoggingFiles:
    def __init__(self, config):
        self.main = config.logging_main_file
        self.errors = config.logging_error_file


class LoggingConfig:
    def __init__(self, config):
        self.level = config.logging_level
        self.format = config.logging_format
        self.files = LoggingFiles(config)


class Config:
    """Main configuration class"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize configuration from YAML file"""
        self.config_path = config_path
        
        # Initialize simple config
        self.simple_config = SimpleConfig()
        
        # Load YAML config if it exists
        self._load_config()
        
        # Setup environment variables
        self._setup_environment_variables()
        
        # Create compatibility objects
        self.data_sources = DataSourceConfig(self.simple_config)
        self.trading = TradingConfig(self.simple_config)
        self.ai_models = AIModelsConfig(self.simple_config)
        self.features = FeaturesConfig(self.simple_config)
        self.database = DatabaseConfig(self.simple_config)
        self.telegram = TelegramConfig(self.simple_config)
        self.dashboard = DashboardConfig(self.simple_config)
        self.logging = LoggingConfig(self.simple_config)
    
    def _load_config(self):
        """Load configuration from YAML file"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    config_data = yaml.safe_load(file)
                
                # Apply YAML values to simple config
                if config_data:
                    self._apply_yaml_config(config_data)
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
    
    def _apply_yaml_config(self, config_data):
        """Apply YAML configuration to simple config"""
        # Trading config
        if 'trading' in config_data:
            trading = config_data['trading']
            if 'symbol' in trading:
                self.simple_config.trading_symbol = trading['symbol']
            if 'timeframes' in trading:
                self.simple_config.trading_timeframes = trading['timeframes']
            
            if 'signals' in trading:
                signals = trading['signals']
                if 'min_confidence' in signals:
                    self.simple_config.signals_min_confidence = signals['min_confidence']
                if 'max_signals_per_day' in signals:
                    self.simple_config.signals_max_signals_per_day = signals['max_signals_per_day']
        
        # AI models config
        if 'ai_models' in config_data:
            ai_models = config_data['ai_models']
            if 'primary_model' in ai_models:
                self.simple_config.ai_models_primary_model = ai_models['primary_model']
            if 'ensemble_models' in ai_models:
                self.simple_config.ai_models_ensemble_models = ai_models['ensemble_models']
        
        # Database config
        if 'database' in config_data:
            database = config_data['database']
            if 'type' in database:
                self.simple_config.database_type = database['type']
            if 'path' in database:
                self.simple_config.database_path = database['path']
        
        # Telegram config
        if 'telegram' in config_data:
            telegram = config_data['telegram']
            if 'enabled' in telegram:
                self.simple_config.telegram_enabled = telegram['enabled']
        
        # Dashboard config
        if 'dashboard' in config_data:
            dashboard = config_data['dashboard']
            if 'enabled' in dashboard:
                self.simple_config.dashboard_enabled = dashboard['enabled']
            if 'host' in dashboard:
                self.simple_config.dashboard_host = dashboard['host']
            if 'port' in dashboard:
                self.simple_config.dashboard_port = dashboard['port']
    
    def _setup_environment_variables(self):
        """Setup environment variables"""
        # API Keys
        self.simple_config.alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY')
        self.simple_config.twelvedata_key = os.getenv('TWELVEDATA_API_KEY')
        
        # Telegram
        self.simple_config.telegram_bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.simple_config.telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        # Database
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            self.simple_config.database_path = database_url
