"""
Telegram bot for signal delivery and user interaction
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from telegram import Update, Bot
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode
from loguru import logger

from ...utils.config import Config
from ...utils.database import DatabaseManager


class TelegramBot:
    """Telegram bot for forex signal delivery"""
    
    def __init__(self, config: Config):
        """Initialize Telegram bot"""
        self.config = config
        self.db_manager = DatabaseManager(config)
        
        # Bot configuration
        self.bot_token = config.telegram.bot_token
        self.chat_id = config.telegram.chat_id
        
        if not self.bot_token:
            raise ValueError("Telegram bot token not configured")
        
        # Create application
        self.application = Application.builder().token(self.bot_token).build()
        self.bot = self.application.bot
        
        # Setup handlers
        self._setup_handlers()
        
        logger.info("Telegram bot initialized")
    
    def _setup_handlers(self):
        """Setup command and message handlers"""
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("stats", self.stats_command))
        self.application.add_handler(CommandHandler("signals", self.recent_signals_command))
        self.application.add_handler(CommandHandler("performance", self.performance_command))
        
        # Message handler for general messages
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = """
🤖 *Welcome to Forex AI Signal Bot!*

I'm your AI-powered trading assistant for XAUUSD (Gold/USD) signals.

*Available Commands:*
/help - Show this help message
/status - Check system status
/stats - View trading statistics
/signals - Show recent signals
/performance - View model performance

I'll automatically send you high-quality trading signals with:
📊 Multi-timeframe analysis (H4, H1, 15M)
🎯 Precise entry, stop loss, and take profit levels
📈 AI confidence scores
🔍 Technical analysis insights

Stay tuned for profitable signals! 🚀
        """
        
        await update.message.reply_text(welcome_message, parse_mode=ParseMode.MARKDOWN)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = """
🆘 *Forex AI Signal Bot Help*

*Commands:*
/start - Welcome message and introduction
/help - Show this help message
/status - Check if the system is running
/stats - View trading statistics (win rate, P&L, etc.)
/signals - Show last 5 signals
/performance - View AI model performance metrics

*Signal Format:*
Each signal includes:
• Direction (BUY/SELL)
• Entry price
• Stop Loss level
• Take Profit level
• Confidence score (0-100%)
• Multi-timeframe analysis

*Risk Warning:*
Trading involves risk. These are AI-generated signals for educational purposes. Always do your own analysis and manage risk appropriately.
        """
        
        await update.message.reply_text(help_message, parse_mode=ParseMode.MARKDOWN)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        try:
            # Check system status
            current_time = datetime.utcnow()
            
            # Get recent signals to check if system is active
            recent_signals = await self.db_manager.get_active_signals()
            
            status_message = f"""
📊 *System Status Report*

🕐 *Current Time:* {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC
🎯 *Symbol:* {self.config.trading.symbol}
📈 *Timeframes:* {', '.join(self.config.trading.timeframes)}
🤖 *AI Models:* {', '.join(self.config.ai_models.ensemble_models)}

🔄 *Active Signals:* {len(recent_signals)}
✅ *Status:* System Running
🔋 *Health:* All systems operational

Next signal check in ~15 minutes.
            """
            
            await update.message.reply_text(status_message, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            logger.error(f"Error in status command: {e}")
            await update.message.reply_text("❌ Error retrieving system status.")
    
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stats command"""
        try:
            # Get trading statistics
            stats = await self.db_manager.get_trade_statistics(days=30)
            
            stats_message = f"""
📊 *Trading Statistics (Last 30 Days)*

📈 *Total Trades:* {stats['total_trades']}
🎯 *Win Rate:* {stats['win_rate']:.1%}
💰 *Total P&L:* {stats['total_pnl']:.2f} pips
📊 *Avg P&L per Trade:* {stats['avg_pnl_per_trade']:.2f} pips
🏆 *Winning Trades:* {stats['winning_trades']}
📉 *Losing Trades:* {stats['losing_trades']}
⚡ *Profit Factor:* {stats['profit_factor']:.2f}

*Performance Rating:* {'🔥 Excellent' if stats['win_rate'] > 0.6 else '👍 Good' if stats['win_rate'] > 0.5 else '⚠️ Needs Improvement'}
            """
            
            await update.message.reply_text(stats_message, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            logger.error(f"Error in stats command: {e}")
            await update.message.reply_text("❌ Error retrieving trading statistics.")
    
    async def recent_signals_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /signals command"""
        try:
            # Get recent signals from database
            signals = await self.db_manager.get_active_signals()
            
            if not signals:
                await update.message.reply_text("📭 No recent signals available.")
                return
            
            signals_message = "📊 *Recent Signals:*\n\n"
            
            for i, signal in enumerate(signals[-5:], 1):  # Last 5 signals
                direction_emoji = "🟢" if signal['direction'] == 'BUY' else "🔴"
                
                signals_message += f"""
{direction_emoji} *Signal #{i}*
📊 Direction: {signal['direction']}
💰 Entry: {signal['entry_price']:.2f}
🛑 Stop Loss: {signal['stop_loss']:.2f}
🎯 Take Profit: {signal['take_profit']:.2f}
📈 Confidence: {signal['confidence']:.1%}
🕐 Time: {signal['timestamp'].strftime('%m-%d %H:%M')}
---
                """
            
            await update.message.reply_text(signals_message, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            logger.error(f"Error in signals command: {e}")
            await update.message.reply_text("❌ Error retrieving recent signals.")
    
    async def performance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /performance command"""
        try:
            performance_message = f"""
🤖 *AI Model Performance*

🧠 *Primary Model:* {self.config.ai_models.primary_model.title()}
🔄 *Ensemble Models:* {len(self.config.ai_models.ensemble_models)}

📊 *Model Configuration:*
• Sequence Length: {self.config.ai_models.transformer.sequence_length}
• Features: 50+ technical indicators
• Timeframes: Multi-timeframe analysis
• Validation: Multi-layer signal validation

🎯 *Signal Quality:*
• Min Confidence: {self.config.trading.signals.min_confidence:.0%}
• Risk/Reward: {self.config.trading.signals.risk_reward_ratio}:1
• Max Daily Signals: {self.config.trading.signals.max_signals_per_day}

🔄 *Self-Learning:* Model retrains automatically based on performance
            """
            
            await update.message.reply_text(performance_message, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            logger.error(f"Error in performance command: {e}")
            await update.message.reply_text("❌ Error retrieving performance data.")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle general messages"""
        user_message = update.message.text.lower()
        
        # Simple keyword responses
        if any(word in user_message for word in ['hello', 'hi', 'hey']):
            await update.message.reply_text("👋 Hello! Use /help to see available commands.")
        elif any(word in user_message for word in ['thanks', 'thank you']):
            await update.message.reply_text("🙏 You're welcome! Happy trading!")
        elif any(word in user_message for word in ['signal', 'trade']):
            await update.message.reply_text("📊 Use /signals to see recent signals or /status for system status.")
        else:
            await update.message.reply_text("🤖 I'm a trading bot. Use /help to see what I can do!")
    
    async def send_signal(self, signal_data: Dict[str, Any]):
        """Send trading signal to Telegram"""
        try:
            if not self.chat_id:
                logger.warning("No Telegram chat ID configured")
                return
            
            # Format signal message
            direction_emoji = "🟢 BUY" if signal_data['direction'] == 'BUY' else "🔴 SELL"
            confidence_emoji = "🔥" if signal_data['confidence'] > 0.8 else "⚡" if signal_data['confidence'] > 0.6 else "📊"
            
            message = f"""
🚨 *XAUUSD Signal Alert* 🚨

{direction_emoji} *{signal_data['direction']}* {confidence_emoji}

💰 *Entry:* {signal_data['entry_price']:.2f}
🛑 *Stop Loss:* {signal_data['stop_loss']:.2f}
🎯 *Take Profit:* {signal_data['take_profit']:.2f}
📈 *Confidence:* {signal_data['confidence']:.1%}

📋 *Multi-Timeframe Analysis:*
• H4 Trend: {signal_data.get('h4_trend', 'Unknown')}
• H1 Momentum: {signal_data.get('h1_momentum', 'Unknown')}
• 15M Pattern: {signal_data.get('m15_pattern', 'Unknown')}

🤖 *Model:* {signal_data.get('model_used', 'AI Ensemble')}
🕐 *Time:* {signal_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')} UTC

⚠️ *Risk Warning:* Trade at your own risk. Always use proper risk management.
            """
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
            
            logger.info(f"Signal sent to Telegram: {signal_data['direction']} at {signal_data['entry_price']}")
            
        except Exception as e:
            logger.error(f"Error sending signal to Telegram: {e}")
    
    async def send_notification(self, message: str, parse_mode: str = ParseMode.MARKDOWN):
        """Send general notification to Telegram"""
        try:
            if not self.chat_id:
                logger.warning("No Telegram chat ID configured")
                return
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=parse_mode
            )
            
            logger.info("Notification sent to Telegram")
            
        except Exception as e:
            logger.error(f"Error sending notification to Telegram: {e}")
    
    async def start(self):
        """Start the Telegram bot"""
        try:
            logger.info("Starting Telegram bot...")
            
            # Send startup notification
            await self.send_notification(
                "🤖 *Forex AI System Started*\n\n"
                "✅ System is now online and monitoring XAUUSD\n"
                "📊 Ready to generate high-quality trading signals\n"
                "🔄 Use /help for available commands"
            )
            
            # Start polling (non-blocking)
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            
            logger.info("Telegram bot started successfully")
            
        except Exception as e:
            logger.error(f"Error starting Telegram bot: {e}")
            raise
    
    async def stop(self):
        """Stop the Telegram bot"""
        try:
            logger.info("Stopping Telegram bot...")
            
            # Send shutdown notification
            await self.send_notification(
                "🤖 *Forex AI System Stopped*\n\n"
                "⏹️ System is going offline\n"
                "📊 Signal generation paused\n"
                "🔄 Will resume when system restarts"
            )
            
            # Stop the application
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()
            
            logger.info("Telegram bot stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping Telegram bot: {e}")
