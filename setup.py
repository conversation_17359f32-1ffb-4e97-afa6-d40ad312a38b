#!/usr/bin/env python3
"""
Setup script for Forex AI Signal Generation System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from typing import List, Optional


def run_command(command: List[str], check: bool = True) -> subprocess.CompletedProcess:
    """Run a command and return the result"""
    print(f"Running: {' '.join(command)}")
    return subprocess.run(command, check=check, capture_output=True, text=True)


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")


def check_dependencies():
    """Check if required system dependencies are available"""
    dependencies = ['git', 'curl']
    
    for dep in dependencies:
        if not shutil.which(dep):
            print(f"❌ {dep} is not installed or not in PATH")
            return False
    
    print("✅ System dependencies check passed")
    return True


def create_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'models', 'deployment']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def setup_environment():
    """Setup environment file"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your API keys and configuration")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ .env.example file not found")


def install_python_dependencies():
    """Install Python dependencies"""
    try:
        print("Installing Python dependencies...")
        run_command([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        run_command([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False


def check_docker():
    """Check if Docker is available"""
    try:
        run_command(['docker', '--version'])
        run_command(['docker-compose', '--version'])
        print("✅ Docker and Docker Compose are available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Docker or Docker Compose not found")
        print("   Docker is optional but recommended for deployment")
        return False


def initialize_database():
    """Initialize database and collect initial data"""
    try:
        print("Initializing database and collecting historical data...")
        print("This may take several minutes...")
        
        result = run_command([sys.executable, 'main.py', '--mode', 'init'], check=False)
        
        if result.returncode == 0:
            print("✅ Database initialized successfully")
            return True
        else:
            print(f"⚠️  Database initialization completed with warnings")
            print(f"Output: {result.stdout}")
            print(f"Errors: {result.stderr}")
            return True  # Continue even with warnings
            
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        return False


def train_initial_models():
    """Train initial AI models"""
    try:
        print("Training initial AI models...")
        print("This may take 30-60 minutes depending on your hardware...")
        
        result = run_command([sys.executable, 'main.py', '--mode', 'train'], check=False)
        
        if result.returncode == 0:
            print("✅ Models trained successfully")
            return True
        else:
            print(f"⚠️  Model training completed with warnings")
            print(f"Output: {result.stdout}")
            print(f"Errors: {result.stderr}")
            return True  # Continue even with warnings
            
    except Exception as e:
        print(f"❌ Failed to train models: {e}")
        return False


def create_systemd_service():
    """Create systemd service file for Linux systems"""
    if os.name != 'posix':
        return
    
    service_content = f"""[Unit]
Description=Forex AI Signal Generation System
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'forex-ai')}
WorkingDirectory={Path.cwd()}
Environment=PATH={Path.cwd()}/venv/bin
ExecStart={sys.executable} main.py --mode full
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path('/tmp/forex-ai.service')
    with open(service_file, 'w') as f:
        f.write(service_content)
    
    print(f"✅ Systemd service file created at {service_file}")
    print("   To install: sudo cp /tmp/forex-ai.service /etc/systemd/system/")
    print("   To enable: sudo systemctl enable forex-ai")
    print("   To start: sudo systemctl start forex-ai")


def main():
    """Main setup function"""
    print("🤖 Forex AI Signal Generation System Setup")
    print("=" * 50)
    
    # Check Python version
    check_python_version()
    
    # Check system dependencies
    if not check_dependencies():
        print("Please install missing dependencies and run setup again")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Setup environment
    setup_environment()
    
    # Install Python dependencies
    if not install_python_dependencies():
        print("Failed to install Python dependencies")
        sys.exit(1)
    
    # Check Docker (optional)
    docker_available = check_docker()
    
    # Ask user about initialization
    print("\n" + "=" * 50)
    print("Setup Options:")
    print("1. Quick setup (skip data collection and model training)")
    print("2. Full setup (initialize data and train models)")
    print("3. Docker setup (build and run with Docker)")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        print("✅ Quick setup completed!")
        print("\nNext steps:")
        print("1. Edit .env file with your API keys")
        print("2. Run: python main.py --mode init")
        print("3. Run: python main.py --mode train")
        print("4. Run: python main.py --mode full")
        
    elif choice == "2":
        print("\nStarting full setup...")
        
        # Initialize database
        if not initialize_database():
            print("❌ Setup failed during database initialization")
            sys.exit(1)
        
        # Train models
        if not train_initial_models():
            print("❌ Setup failed during model training")
            sys.exit(1)
        
        print("✅ Full setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your Telegram bot token and chat ID")
        print("2. Run: python main.py --mode full")
        
    elif choice == "3":
        if not docker_available:
            print("❌ Docker is not available")
            sys.exit(1)
        
        print("\nBuilding Docker image...")
        try:
            run_command(['docker-compose', 'build'])
            print("✅ Docker image built successfully!")
            print("\nNext steps:")
            print("1. Edit .env file with your API keys")
            print("2. Run: docker-compose up -d")
            print("3. Check logs: docker-compose logs -f")
        except subprocess.CalledProcessError as e:
            print(f"❌ Docker build failed: {e}")
            sys.exit(1)
    
    else:
        print("Invalid choice. Exiting.")
        sys.exit(1)
    
    # Create systemd service (Linux only)
    create_systemd_service()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed!")
    print("\nUseful commands:")
    print("  python main.py --help          # Show all options")
    print("  python main.py --mode status   # Check system status")
    print("  python main.py --mode signals  # Run signal generation only")
    print("  python main.py --mode dashboard # Run web dashboard only")
    print("\nWeb Dashboard: http://localhost:8000")
    print("API Documentation: http://localhost:8000/docs")
    print("\n⚠️  Remember to configure your .env file before running!")


if __name__ == "__main__":
    main()
