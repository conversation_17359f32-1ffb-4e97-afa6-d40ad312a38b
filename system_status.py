#!/usr/bin/env python3
"""
Comprehensive system status report
"""

import sys
import asyncio
import requests
from pathlib import Path
from datetime import datetime

# Add src to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.database import DatabaseManager
from src.utils.config_simple import Config

async def system_status_report():
    """Generate comprehensive system status report"""
    print("🚀 FOREX AI SIGNAL GENERATION SYSTEM STATUS REPORT")
    print("=" * 60)
    print(f"📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        config = Config()
        db = DatabaseManager(config)
        
        # 1. Configuration Status
        print("⚙️  CONFIGURATION STATUS")
        print("-" * 30)
        print(f"✅ Trading Symbol: {config.trading.symbol}")
        print(f"✅ Timeframes: {', '.join(config.trading.timeframes)}")
        print(f"✅ Database Type: {config.database.type}")
        print(f"✅ Database Path: {config.database.path}")
        print(f"✅ Dashboard Enabled: {config.dashboard.enabled}")
        print(f"✅ Dashboard Port: {config.dashboard.port}")
        print(f"✅ Telegram Enabled: {config.telegram.enabled}")
        print()
        
        # 2. Database Status
        print("🗄️  DATABASE STATUS")
        print("-" * 30)
        
        # Check data for each timeframe
        for timeframe in config.trading.timeframes:
            data = await db.get_historical_data('XAUUSD', timeframe, 1)
            count_query = f"SELECT COUNT(*) FROM historical_data WHERE symbol='XAUUSD' AND timeframe='{timeframe}'"
            # For simplicity, we'll use the length of returned data as an estimate
            data_all = await db.get_historical_data('XAUUSD', timeframe, 10000)
            print(f"✅ {timeframe} Data: {len(data_all)} records")
        
        # Check signals
        active_signals = await db.get_active_signals()
        print(f"✅ Active Signals: {len(active_signals)}")
        print()
        
        # 3. API Status
        print("🌐 API STATUS")
        print("-" * 30)
        try:
            # Test health endpoint
            response = requests.get("http://localhost:8000/api/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API Health: {health_data['status']}")
                print(f"✅ API Version: {health_data['version']}")
            else:
                print(f"❌ API Health: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ API Health: Not accessible ({str(e)[:50]}...)")
        
        try:
            # Test signals endpoint
            response = requests.get("http://localhost:8000/api/signals/active", timeout=5)
            if response.status_code == 200:
                signals_data = response.json()
                print(f"✅ Signals API: {len(signals_data)} active signals")
            else:
                print(f"❌ Signals API: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ Signals API: Not accessible")
        print()
        
        # 4. File System Status
        print("📁 FILE SYSTEM STATUS")
        print("-" * 30)
        
        # Check directories
        directories = ['data', 'logs', 'models', 'deployment']
        for directory in directories:
            if Path(directory).exists():
                print(f"✅ {directory}/ directory exists")
            else:
                print(f"❌ {directory}/ directory missing")
        
        # Check key files
        key_files = [
            'config.yaml',
            'requirements.txt',
            'main.py',
            'data/forex_ai.db'
        ]
        for file_path in key_files:
            if Path(file_path).exists():
                size = Path(file_path).stat().st_size
                print(f"✅ {file_path} ({size:,} bytes)")
            else:
                print(f"❌ {file_path} missing")
        print()
        
        # 5. Model Status
        print("🤖 MODEL STATUS")
        print("-" * 30)
        model_files = [
            'models/transformer_best.pth',
            'models/cnn_lstm_best.pth',
            'models/xgboost_best.pth'
        ]
        for model_file in model_files:
            if Path(model_file).exists():
                size = Path(model_file).stat().st_size
                print(f"✅ {model_file} ({size:,} bytes)")
            else:
                print(f"⏳ {model_file} (training in progress)")
        print()
        
        # 6. Recent Activity
        print("📊 RECENT ACTIVITY")
        print("-" * 30)
        if active_signals:
            latest_signal = active_signals[0]
            print(f"🎯 Latest Signal: {latest_signal['direction']} {latest_signal['symbol']}")
            print(f"   Confidence: {latest_signal['confidence']:.2%}")
            print(f"   Entry: ${latest_signal['entry_price']}")
            print(f"   Time: {latest_signal['timestamp']}")
        else:
            print("📭 No active signals")
        print()
        
        # 7. System Recommendations
        print("💡 SYSTEM RECOMMENDATIONS")
        print("-" * 30)
        
        recommendations = []
        
        # Check if models exist
        if not any(Path(f).exists() for f in model_files):
            recommendations.append("🔄 Run model training: python main.py --mode train")
        
        # Check if Telegram is configured
        if not config.telegram.bot_token:
            recommendations.append("📱 Configure Telegram bot token in .env file")
        
        # Check data freshness
        latest_data = await db.get_historical_data('XAUUSD', '15m', 1)
        if not latest_data.empty:
            latest_time = latest_data.iloc[0]['timestamp']
            print(f"📈 Latest data: {latest_time}")
            recommendations.append("🔄 Consider running data update for real-time trading")
        
        if recommendations:
            for rec in recommendations:
                print(rec)
        else:
            print("✅ System is optimally configured!")
        
        print()
        print("🎉 SYSTEM STATUS: OPERATIONAL")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error generating status report: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(system_status_report())
