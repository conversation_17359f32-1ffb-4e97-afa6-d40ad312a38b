"""
Integration script to load and test Colab-trained models in local environment
"""

import os
import json
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from loguru import logger
import joblib
from typing import Dict, Any, Tuple
import zipfile
import shutil

# Import your existing modules
from src.utils.config import Config
from src.models.ai_models.transformer_model import ForexTransformer
from src.models.ai_models.cnn_lstm_model import CNNLSTMModel


class ColabModelIntegrator:
    """Integrate Colab-trained models into local system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize model integrator"""
        self.config = Config(config_path)
        self.models_dir = Path("models/saved_models")
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("Colab model integrator initialized")
    
    def extract_colab_models(self, zip_path: str) -> Dict[str, str]:
        """Extract downloaded Colab models zip file"""
        try:
            logger.info(f"Extracting Colab models from {zip_path}")
            
            # Create extraction directory
            extract_dir = Path("colab_models_extracted")
            extract_dir.mkdir(exist_ok=True)
            
            # Extract zip file
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            logger.info(f"Models extracted to {extract_dir}")
            
            # Find model directories
            model_dirs = {}
            for item in extract_dir.iterdir():
                if item.is_dir() and item.name.endswith('_final'):
                    model_name = item.name.replace('_final', '')
                    model_dirs[model_name] = str(item)
            
            logger.info(f"Found models: {list(model_dirs.keys())}")
            return model_dirs
            
        except Exception as e:
            logger.error(f"Error extracting Colab models: {e}")
            raise
    
    def load_model_metadata(self, model_dir: str) -> Dict[str, Any]:
        """Load model metadata from Colab training"""
        try:
            metadata_file = Path(model_dir) / f"{Path(model_dir).name.replace('_final', '')}_metadata.json"
            
            if not metadata_file.exists():
                # Try alternative naming
                for file in Path(model_dir).glob("*_metadata.json"):
                    metadata_file = file
                    break
            
            if not metadata_file.exists():
                raise FileNotFoundError(f"Metadata file not found in {model_dir}")
            
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            logger.info(f"Loaded metadata for {metadata['model_name']}")
            return metadata
            
        except Exception as e:
            logger.error(f"Error loading model metadata: {e}")
            raise
    
    def create_local_model(self, model_name: str, metadata: Dict[str, Any]) -> torch.nn.Module:
        """Create local model instance matching Colab architecture"""
        try:
            input_features = metadata['input_features']
            
            if model_name == 'transformer':
                model = ForexTransformer(self.config)
                # Adjust input projection if needed
                if hasattr(model, 'input_projection'):
                    model.input_projection = torch.nn.Linear(input_features, model.d_model)
                    
            elif model_name == 'cnn_lstm':
                model = CNNLSTMModel(self.config, input_features)
                
            else:
                raise ValueError(f"Unknown model type: {model_name}")
            
            logger.info(f"Created local {model_name} model with {sum(p.numel() for p in model.parameters())} parameters")
            return model
            
        except Exception as e:
            logger.error(f"Error creating local model: {e}")
            raise
    
    def load_colab_weights(self, model: torch.nn.Module, model_dir: str, model_name: str) -> torch.nn.Module:
        """Load Colab-trained weights into local model"""
        try:
            # Find model weights file
            weights_file = Path(model_dir) / f"{model_name}.pth"
            
            if not weights_file.exists():
                # Try alternative naming
                for file in Path(model_dir).glob("*.pth"):
                    if not file.name.endswith('_best.pth'):
                        weights_file = file
                        break
            
            if not weights_file.exists():
                raise FileNotFoundError(f"Model weights not found in {model_dir}")
            
            # Load weights
            state_dict = torch.load(weights_file, map_location='cpu')
            
            # Handle potential architecture differences
            try:
                model.load_state_dict(state_dict, strict=True)
                logger.info(f"Loaded weights for {model_name} (strict mode)")
            except RuntimeError as e:
                logger.warning(f"Strict loading failed, trying flexible loading: {e}")
                # Try flexible loading
                model_dict = model.state_dict()
                filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
                model_dict.update(filtered_dict)
                model.load_state_dict(model_dict)
                logger.info(f"Loaded weights for {model_name} (flexible mode)")
            
            return model
            
        except Exception as e:
            logger.error(f"Error loading Colab weights: {e}")
            raise
    
    def test_model_inference(self, model: torch.nn.Module, model_name: str, 
                           scaler_path: str, sequence_length: int) -> Dict[str, Any]:
        """Test model inference with dummy data"""
        try:
            logger.info(f"Testing {model_name} inference...")
            
            # Load scaler if available
            scaler = None
            if os.path.exists(scaler_path):
                scaler = joblib.load(scaler_path)
                logger.info("Loaded preprocessing scaler")
            
            # Create dummy input data
            if scaler:
                input_features = scaler.n_features_in_
            else:
                # Estimate from model
                if hasattr(model, 'input_projection'):
                    input_features = model.input_projection.in_features
                else:
                    input_features = 50  # Default
            
            # Generate dummy sequence data
            dummy_data = np.random.randn(1, sequence_length, input_features)
            
            if scaler:
                # Reshape for scaling
                original_shape = dummy_data.shape
                dummy_data = dummy_data.reshape(-1, input_features)
                dummy_data = scaler.transform(dummy_data)
                dummy_data = dummy_data.reshape(original_shape)
            
            # Convert to tensor
            dummy_tensor = torch.FloatTensor(dummy_data)
            
            # Test inference
            model.eval()
            with torch.no_grad():
                predictions, confidence = model(dummy_tensor)
            
            # Get results
            predicted_class = torch.argmax(predictions, dim=1).item()
            confidence_score = confidence.item() if confidence.dim() == 1 else confidence[0].item()
            
            results = {
                'model_name': model_name,
                'input_shape': list(dummy_tensor.shape),
                'output_shape': list(predictions.shape),
                'predicted_class': predicted_class,
                'confidence_score': confidence_score,
                'class_probabilities': torch.softmax(predictions, dim=1)[0].tolist(),
                'inference_successful': True
            }
            
            logger.info(f"✅ {model_name} inference test successful")
            logger.info(f"  Predicted class: {predicted_class}")
            logger.info(f"  Confidence: {confidence_score:.4f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error testing {model_name} inference: {e}")
            return {
                'model_name': model_name,
                'inference_successful': False,
                'error': str(e)
            }
    
    def save_integrated_model(self, model: torch.nn.Module, model_name: str, 
                            metadata: Dict[str, Any], test_results: Dict[str, Any]):
        """Save integrated model to local models directory"""
        try:
            # Create model save directory
            save_dir = self.models_dir / f"{model_name}_colab_trained"
            save_dir.mkdir(exist_ok=True)
            
            # Save model
            model_file = save_dir / f"{model_name}.pth"
            torch.save(model.state_dict(), model_file)
            
            # Save integration metadata
            integration_metadata = {
                'model_name': model_name,
                'source': 'google_colab',
                'integration_date': pd.Timestamp.now().isoformat(),
                'original_metadata': metadata,
                'test_results': test_results,
                'local_path': str(model_file),
                'model_class': type(model).__name__
            }
            
            metadata_file = save_dir / f"{model_name}_integration.json"
            with open(metadata_file, 'w') as f:
                json.dump(integration_metadata, f, indent=2)
            
            logger.info(f"✅ {model_name} saved to {save_dir}")
            return str(save_dir)
            
        except Exception as e:
            logger.error(f"Error saving integrated model: {e}")
            raise
    
    def update_config(self, integrated_models: Dict[str, str]):
        """Update config.yaml with new model paths"""
        try:
            logger.info("Updating configuration with Colab-trained models...")
            
            # Create backup of current config
            config_backup = Path("config_backup.yaml")
            shutil.copy2("config.yaml", config_backup)
            logger.info(f"Config backup created: {config_backup}")
            
            # Update model paths in config
            # This is a simplified update - you may need to adjust based on your config structure
            config_updates = {
                'ai_models': {
                    'model_paths': {
                        'transformer': integrated_models.get('transformer', ''),
                        'cnn_lstm': integrated_models.get('cnn_lstm', '')
                    },
                    'use_colab_models': True,
                    'colab_integration_date': pd.Timestamp.now().isoformat()
                }
            }
            
            logger.info("✅ Configuration updated with Colab model paths")
            logger.info("Note: You may need to manually adjust config.yaml for your specific setup")
            
            return config_updates
            
        except Exception as e:
            logger.error(f"Error updating config: {e}")
            raise
    
    def integrate_all_models(self, zip_path: str) -> Dict[str, Any]:
        """Complete integration process for all Colab models"""
        try:
            logger.info("🚀 Starting Colab model integration...")
            
            # Extract models
            model_dirs = self.extract_colab_models(zip_path)
            
            integration_results = {}
            integrated_models = {}
            
            for model_name, model_dir in model_dirs.items():
                logger.info(f"Integrating {model_name}...")
                
                try:
                    # Load metadata
                    metadata = self.load_model_metadata(model_dir)
                    
                    # Create local model
                    model = self.create_local_model(model_name, metadata)
                    
                    # Load Colab weights
                    model = self.load_colab_weights(model, model_dir, model_name)
                    
                    # Test inference
                    scaler_path = Path(model_dir) / f"{model_name}_scaler.pkl"
                    test_results = self.test_model_inference(
                        model, model_name, str(scaler_path), 
                        metadata.get('sequence_length', 100)
                    )
                    
                    # Save integrated model
                    save_path = self.save_integrated_model(model, model_name, metadata, test_results)
                    integrated_models[model_name] = save_path
                    
                    integration_results[model_name] = {
                        'status': 'success',
                        'metadata': metadata,
                        'test_results': test_results,
                        'save_path': save_path
                    }
                    
                    logger.info(f"✅ {model_name} integration completed")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to integrate {model_name}: {e}")
                    integration_results[model_name] = {
                        'status': 'failed',
                        'error': str(e)
                    }
            
            # Update configuration
            if integrated_models:
                config_updates = self.update_config(integrated_models)
                integration_results['config_updates'] = config_updates
            
            logger.info("🎉 Colab model integration completed!")
            return integration_results
            
        except Exception as e:
            logger.error(f"Error during model integration: {e}")
            raise


def main():
    """Main integration function"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python integrate_colab_models.py <path_to_colab_models.zip>")
        sys.exit(1)
    
    zip_path = sys.argv[1]
    
    if not os.path.exists(zip_path):
        print(f"Error: Zip file not found: {zip_path}")
        sys.exit(1)
    
    try:
        integrator = ColabModelIntegrator()
        results = integrator.integrate_all_models(zip_path)
        
        print("\n" + "="*60)
        print("COLAB MODEL INTEGRATION COMPLETED!")
        print("="*60)
        
        for model_name, result in results.items():
            if model_name == 'config_updates':
                continue
                
            print(f"\n📊 {model_name.upper()}:")
            if result['status'] == 'success':
                print(f"  ✅ Status: Successfully integrated")
                print(f"  📁 Path: {result['save_path']}")
                if result['test_results']['inference_successful']:
                    print(f"  🧠 Inference: Working correctly")
                    print(f"  🎯 Confidence: {result['test_results']['confidence_score']:.4f}")
                else:
                    print(f"  ⚠️  Inference: Failed - {result['test_results'].get('error', 'Unknown error')}")
            else:
                print(f"  ❌ Status: Integration failed")
                print(f"  🔍 Error: {result['error']}")
        
        print(f"\n📋 Next Steps:")
        print(f"  1. Review integrated models in 'models/saved_models/' directory")
        print(f"  2. Test models with your actual data")
        print(f"  3. Update your main application to use the new models")
        print(f"  4. Monitor model performance in production")
        
        print(f"\n🚀 Your Colab-trained models are ready to use!")
        
    except Exception as e:
        print(f"\n❌ Integration failed: {e}")
        logger.error(f"Integration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
