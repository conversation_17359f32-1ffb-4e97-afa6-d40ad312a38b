"""
Advanced Colab Training Script for Production-Ready Forex AI Models
Implements state-of-the-art training techniques for maximum performance
"""

import os
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
from torch.optim.swa_utils import AveragedModel, SWALR
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import optuna
from typing import Dict, Any, Tuple, List, Optional
import warnings
import random
from datetime import datetime

# Import advanced configurations
from advanced_training_config import (
    AdvancedTrainingConfig, FocalLoss, LabelSmoothingLoss, MixupLoss, EMA,
    WarmupScheduler, mixup_data, cutmix_data, get_advanced_optimizer,
    get_advanced_scheduler, get_advanced_loss_function, AdvancedTransformerConfig
)

warnings.filterwarnings('ignore')


class AdvancedForexTrainer:
    """Advanced trainer with state-of-the-art techniques"""
    
    def __init__(self, config: Dict[str, Any], device: torch.device):
        self.config = config
        self.device = device
        self.training_history = []
        self.best_metrics = {}
        
        # Initialize advanced components
        self.ema = None
        self.swa_model = None
        self.warmup_scheduler = None
        
        print(f"🚀 Advanced Trainer initialized with {len([k for k, v in config.items() if isinstance(v, bool) and v])} advanced features")
    
    def setup_advanced_training(self, model: nn.Module) -> Tuple[torch.optim.Optimizer, Optional[torch.optim.lr_scheduler._LRScheduler], nn.Module]:
        """Setup advanced training components"""
        
        # Advanced optimizer
        optimizer = get_advanced_optimizer(model, self.config)
        
        # Advanced scheduler
        scheduler = get_advanced_scheduler(optimizer, self.config)
        
        # Advanced loss function
        criterion = get_advanced_loss_function(self.config)
        
        # EMA setup
        if self.config.get('use_ema', False):
            self.ema = EMA(model, decay=self.config.get('ema_decay', 0.999))
            print("✅ EMA initialized")
        
        # SWA setup
        if self.config.get('use_swa', False):
            self.swa_model = AveragedModel(model)
            swa_start = self.config.get('swa_start', self.config['epochs'] // 2)
            swa_lr = self.config.get('swa_lr', self.config['learning_rate'] * 0.1)
            self.swa_scheduler = SWALR(optimizer, swa_lr=swa_lr)
            print(f"✅ SWA initialized (start: epoch {swa_start})")
        
        # Warmup scheduler
        if self.config.get('warmup_epochs', 0) > 0:
            self.warmup_scheduler = WarmupScheduler(
                optimizer, 
                self.config['warmup_epochs'], 
                self.config['learning_rate']
            )
            print(f"✅ Warmup scheduler initialized ({self.config['warmup_epochs']} epochs)")
        
        return optimizer, scheduler, criterion
    
    def advanced_train_epoch(self, model: nn.Module, train_loader: DataLoader, 
                           optimizer: torch.optim.Optimizer, criterion: nn.Module, 
                           epoch: int) -> Dict[str, float]:
        """Advanced training epoch with multiple techniques"""
        model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        # Mixed precision training
        use_amp = torch.cuda.is_available() and hasattr(torch.cuda.amp, 'autocast')
        if use_amp:
            scaler = torch.cuda.amp.GradScaler()
        
        # Gradient accumulation
        accumulation_steps = self.config.get('gradient_accumulation', 1)
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}")
        
        for batch_idx, (data, targets) in enumerate(progress_bar):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # Apply data augmentation
            if self.config.get('mixup_alpha', 0) > 0 and np.random.random() < 0.5:
                data, targets_a, targets_b, lam = mixup_data(data, targets, self.config['mixup_alpha'])
                use_mixup = True
            elif self.config.get('cutmix_alpha', 0) > 0 and np.random.random() < 0.3:
                data, targets_a, targets_b, lam = cutmix_data(data, targets, self.config['cutmix_alpha'])
                use_mixup = True
            else:
                use_mixup = False
            
            # Forward pass with mixed precision
            if use_amp:
                with torch.cuda.amp.autocast():
                    predictions, confidence = model(data)
                    
                    if use_mixup:
                        mixup_criterion = MixupLoss(criterion)
                        loss = mixup_criterion(predictions, targets_a, targets_b, lam)
                    else:
                        loss = criterion(predictions, targets)
                    
                    loss = loss / accumulation_steps
            else:
                predictions, confidence = model(data)
                
                if use_mixup:
                    mixup_criterion = MixupLoss(criterion)
                    loss = mixup_criterion(predictions, targets_a, targets_b, lam)
                else:
                    loss = criterion(predictions, targets)
                
                loss = loss / accumulation_steps
            
            # Backward pass
            if use_amp:
                scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # Gradient accumulation and clipping
            if (batch_idx + 1) % accumulation_steps == 0:
                if self.config.get('gradient_clipping', 0) > 0:
                    if use_amp:
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), self.config['gradient_clipping'])
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        torch.nn.utils.clip_grad_norm_(model.parameters(), self.config['gradient_clipping'])
                        optimizer.step()
                else:
                    if use_amp:
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        optimizer.step()
                
                optimizer.zero_grad()
                
                # Update EMA
                if self.ema is not None:
                    self.ema.update()
            
            # Statistics
            total_loss += loss.item() * accumulation_steps
            if not use_mixup:
                _, predicted = predictions.max(1)
                total += targets.size(0)
                correct += predicted.eq(targets).sum().item()
            
            # Update progress bar
            if not use_mixup:
                accuracy = 100. * correct / total if total > 0 else 0
                progress_bar.set_postfix({
                    'Loss': f'{total_loss/(batch_idx+1):.4f}',
                    'Acc': f'{accuracy:.2f}%'
                })
        
        # Warmup scheduler step
        if self.warmup_scheduler is not None and epoch < self.config.get('warmup_epochs', 0):
            self.warmup_scheduler.step()
        
        epoch_loss = total_loss / len(train_loader)
        epoch_acc = 100. * correct / total if total > 0 else 0
        
        return {
            'loss': epoch_loss,
            'accuracy': epoch_acc,
            'learning_rate': optimizer.param_groups[0]['lr']
        }
    
    def advanced_evaluate(self, model: nn.Module, val_loader: DataLoader, 
                         criterion: nn.Module) -> Dict[str, float]:
        """Advanced evaluation with multiple metrics"""
        model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        all_confidences = []
        
        with torch.no_grad():
            for data, targets in tqdm(val_loader, desc="Evaluating"):
                data, targets = data.to(self.device), targets.to(self.device)
                
                # Test Time Augmentation (TTA)
                if self.config.get('test_time_augmentation', False):
                    predictions_list = []
                    confidence_list = []
                    
                    # Original prediction
                    pred, conf = model(data)
                    predictions_list.append(pred)
                    confidence_list.append(conf)
                    
                    # Augmented predictions (simple noise addition)
                    for _ in range(3):
                        noise = torch.randn_like(data) * 0.01
                        pred_aug, conf_aug = model(data + noise)
                        predictions_list.append(pred_aug)
                        confidence_list.append(conf_aug)
                    
                    # Average predictions
                    predictions = torch.stack(predictions_list).mean(0)
                    confidence = torch.stack(confidence_list).mean(0)
                else:
                    predictions, confidence = model(data)
                
                loss = criterion(predictions, targets)
                total_loss += loss.item()
                
                # Store predictions
                _, predicted = predictions.max(1)
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_confidences.extend(confidence.cpu().numpy())
        
        # Calculate comprehensive metrics
        accuracy = accuracy_score(all_targets, all_predictions)
        precision = precision_score(all_targets, all_predictions, average='weighted', zero_division=0)
        recall = recall_score(all_targets, all_predictions, average='weighted', zero_division=0)
        f1 = f1_score(all_targets, all_predictions, average='weighted', zero_division=0)
        
        # Confidence calibration metrics
        avg_confidence = np.mean(all_confidences)
        confidence_std = np.std(all_confidences)
        
        return {
            'loss': total_loss / len(val_loader),
            'accuracy': accuracy * 100,
            'precision': precision * 100,
            'recall': recall * 100,
            'f1_score': f1 * 100,
            'avg_confidence': avg_confidence,
            'confidence_std': confidence_std
        }
    
    def train_with_cross_validation(self, X: np.ndarray, y: np.ndarray, 
                                  model_class, model_kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Train with k-fold cross validation"""
        if not self.config.get('use_kfold', False):
            return self.train_single_fold(X, y, model_class, model_kwargs)
        
        n_folds = self.config.get('n_folds', 5)
        kfold = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        
        fold_results = []
        fold_models = []
        
        print(f"🔄 Starting {n_folds}-fold cross validation...")
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(X, y)):
            print(f"\n📊 Training Fold {fold + 1}/{n_folds}")
            
            # Split data
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # Train fold
            fold_result = self.train_single_fold(
                X_train_fold, y_train_fold, model_class, model_kwargs,
                X_val_fold, y_val_fold, fold_id=fold
            )
            
            fold_results.append(fold_result)
            fold_models.append(fold_result['model'])
        
        # Aggregate results
        avg_metrics = {}
        for metric in ['accuracy', 'precision', 'recall', 'f1_score']:
            values = [result['final_metrics'][metric] for result in fold_results]
            avg_metrics[f'{metric}_mean'] = np.mean(values)
            avg_metrics[f'{metric}_std'] = np.std(values)
        
        return {
            'fold_results': fold_results,
            'fold_models': fold_models,
            'avg_metrics': avg_metrics,
            'best_fold': np.argmax([r['final_metrics']['f1_score'] for r in fold_results])
        }
    
    def train_single_fold(self, X_train: np.ndarray, y_train: np.ndarray, 
                         model_class, model_kwargs: Dict[str, Any],
                         X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None,
                         fold_id: Optional[int] = None) -> Dict[str, Any]:
        """Train a single fold or full dataset"""
        
        # Split data if validation not provided
        if X_val is None or y_val is None:
            X_train, X_val, y_train, y_val = train_test_split(
                X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
            )
        
        # Create data loaders
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train).to(self.device),
            torch.LongTensor(y_train).to(self.device)
        )
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val).to(self.device),
            torch.LongTensor(y_val).to(self.device)
        )
        
        train_loader = DataLoader(train_dataset, batch_size=self.config['batch_size'], shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.config['batch_size'], shuffle=False)
        
        # Create model
        model = model_class(**model_kwargs).to(self.device)
        
        # Setup advanced training
        optimizer, scheduler, criterion = self.setup_advanced_training(model)
        
        # Training loop
        best_f1 = 0.0
        patience_counter = 0
        fold_history = []
        
        fold_suffix = f"_fold_{fold_id}" if fold_id is not None else ""
        
        for epoch in range(self.config['epochs']):
            # Train epoch
            train_metrics = self.advanced_train_epoch(model, train_loader, optimizer, criterion, epoch)
            
            # Evaluate
            val_metrics = self.advanced_evaluate(model, val_loader, criterion)
            
            # Scheduler step
            if scheduler is not None and epoch >= self.config.get('warmup_epochs', 0):
                if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    scheduler.step(val_metrics['loss'])
                else:
                    scheduler.step()
            
            # SWA update
            if self.swa_model is not None and epoch >= self.config.get('swa_start', self.config['epochs'] // 2):
                self.swa_model.update_parameters(model)
                if epoch == self.config.get('swa_start', self.config['epochs'] // 2):
                    print("✅ SWA started")
            
            # Log progress
            print(f"Epoch {epoch+1}/{self.config['epochs']} - "
                  f"Train Loss: {train_metrics['loss']:.4f}, Train Acc: {train_metrics['accuracy']:.2f}% - "
                  f"Val Loss: {val_metrics['loss']:.4f}, Val Acc: {val_metrics['accuracy']:.2f}%, "
                  f"Val F1: {val_metrics['f1_score']:.2f}%")
            
            # Save history
            epoch_data = {
                'epoch': epoch + 1,
                'train_loss': train_metrics['loss'],
                'train_accuracy': train_metrics['accuracy'],
                'val_loss': val_metrics['loss'],
                'val_accuracy': val_metrics['accuracy'],
                'val_f1': val_metrics['f1_score'],
                'learning_rate': train_metrics['learning_rate']
            }
            fold_history.append(epoch_data)
            
            # Early stopping and best model saving
            if val_metrics['f1_score'] > best_f1:
                best_f1 = val_metrics['f1_score']
                patience_counter = 0
                
                # Save best model
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'metrics': val_metrics,
                    'config': self.config
                }, f'best_model{fold_suffix}.pth')
                
                self.best_metrics = val_metrics.copy()
            else:
                patience_counter += 1
                if patience_counter >= self.config.get('early_stopping_patience', 25):
                    print(f"Early stopping triggered after {epoch+1} epochs")
                    break
        
        # Final SWA evaluation if used
        if self.swa_model is not None:
            print("🔄 Evaluating SWA model...")
            swa_metrics = self.advanced_evaluate(self.swa_model, val_loader, criterion)
            if swa_metrics['f1_score'] > self.best_metrics['f1_score']:
                print("✅ SWA model is better, using SWA weights")
                self.best_metrics = swa_metrics
                model = self.swa_model
        
        return {
            'model': model,
            'final_metrics': self.best_metrics,
            'training_history': fold_history,
            'fold_id': fold_id
        }
