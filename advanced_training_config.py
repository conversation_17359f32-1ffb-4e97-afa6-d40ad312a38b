"""
Advanced Training Configuration for Production-Ready Forex AI Models
Implements state-of-the-art techniques for maximum accuracy and stability
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.swa_utils import AveragedModel, SWALR
import numpy as np
import math
from typing import Dict, Any, Optional, Tuple


class AdvancedTrainingConfig:
    """Advanced training configuration with production-ready settings"""
    
    CONFIGS = {
        "quick": {
            # Basic parameters
            "epochs": 50,
            "batch_size": 32,
            "learning_rate": 0.001,
            "sequence_length": 80,
            
            # Advanced techniques
            "warmup_epochs": 5,
            "use_scheduler": True,
            "scheduler_type": "cosine",
            "gradient_clipping": 1.0,
            "label_smoothing": 0.1,
            "mixup_alpha": 0.2,
            "use_swa": False,
            "use_ema": True,
            "ema_decay": 0.999,
            
            # Optimization
            "optimizer": "adamw",
            "weight_decay": 0.01,
            "hyperopt_trials": 15,
            "use_focal_loss": True,
            "focal_alpha": 0.25,
            "focal_gamma": 2.0,
            
            # Regularization
            "dropout_schedule": True,
            "gradient_accumulation": 2,
            "early_stopping_patience": 15
        },
        
        "production": {
            # Basic parameters
            "epochs": 200,
            "batch_size": 24,
            "learning_rate": 0.0008,
            "sequence_length": 150,
            
            # Advanced techniques
            "warmup_epochs": 15,
            "use_scheduler": True,
            "scheduler_type": "cosine_restarts",
            "t_0": 50,
            "t_mult": 2,
            "gradient_clipping": 0.8,
            "label_smoothing": 0.15,
            "mixup_alpha": 0.4,
            "cutmix_alpha": 1.0,
            "use_swa": True,
            "swa_start": 150,
            "swa_lr": 0.0001,
            "use_ema": True,
            "ema_decay": 0.9995,
            
            # Optimization
            "optimizer": "ranger",  # RAdam + Lookahead
            "weight_decay": 0.02,
            "hyperopt_trials": 75,
            "use_focal_loss": True,
            "focal_alpha": 0.25,
            "focal_gamma": 2.5,
            
            # Advanced regularization
            "dropout_schedule": True,
            "gradient_accumulation": 4,
            "use_sam": True,  # Sharpness-Aware Minimization
            "sam_rho": 0.05,
            "early_stopping_patience": 25,
            
            # Cross-validation
            "use_kfold": True,
            "n_folds": 5,
            
            # Model ensemble
            "ensemble_models": True,
            "snapshot_ensemble": True
        },
        
        "research": {
            # Basic parameters
            "epochs": 350,
            "batch_size": 16,
            "learning_rate": 0.0005,
            "sequence_length": 200,
            
            # Advanced techniques
            "warmup_epochs": 25,
            "use_scheduler": True,
            "scheduler_type": "cosine_restarts",
            "t_0": 70,
            "t_mult": 2,
            "gradient_clipping": 0.5,
            "label_smoothing": 0.2,
            "mixup_alpha": 0.6,
            "cutmix_alpha": 1.2,
            "use_swa": True,
            "swa_start": 250,
            "swa_lr": 0.00005,
            "use_ema": True,
            "ema_decay": 0.9998,
            
            # Optimization
            "optimizer": "ranger",
            "weight_decay": 0.03,
            "hyperopt_trials": 150,
            "use_focal_loss": True,
            "focal_alpha": 0.3,
            "focal_gamma": 3.0,
            
            # Advanced regularization
            "dropout_schedule": True,
            "gradient_accumulation": 8,
            "use_sam": True,
            "sam_rho": 0.1,
            "early_stopping_patience": 35,
            
            # Cross-validation
            "use_kfold": True,
            "n_folds": 10,
            
            # Model ensemble
            "ensemble_models": True,
            "snapshot_ensemble": True,
            "multi_sample_dropout": True,
            "test_time_augmentation": True
        }
    }
    
    @classmethod
    def get_config(cls, mode: str = "production") -> Dict[str, Any]:
        """Get training configuration for specified mode"""
        if mode not in cls.CONFIGS:
            raise ValueError(f"Unknown training mode: {mode}. Available: {list(cls.CONFIGS.keys())}")
        return cls.CONFIGS[mode].copy()
    
    @classmethod
    def get_time_estimate(cls, mode: str) -> str:
        """Get estimated training time for mode"""
        estimates = {
            "quick": "1-2 hours",
            "production": "4-6 hours",
            "research": "8-12 hours"
        }
        return estimates.get(mode, "Unknown")


class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance"""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class LabelSmoothingLoss(nn.Module):
    """Label Smoothing for better generalization"""
    
    def __init__(self, num_classes: int, smoothing: float = 0.1):
        super().__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        pred = pred.log_softmax(dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(pred)
            true_dist.fill_(self.smoothing / (self.num_classes - 1))
            true_dist.scatter_(1, target.data.unsqueeze(1), self.confidence)
        return torch.mean(torch.sum(-true_dist * pred, dim=-1))


class MixupLoss(nn.Module):
    """Mixup data augmentation loss"""
    
    def __init__(self, criterion):
        super().__init__()
        self.criterion = criterion
    
    def forward(self, pred: torch.Tensor, y_a: torch.Tensor, y_b: torch.Tensor, lam: float) -> torch.Tensor:
        return lam * self.criterion(pred, y_a) + (1 - lam) * self.criterion(pred, y_b)


class EMA:
    """Exponential Moving Average for model parameters"""
    
    def __init__(self, model: nn.Module, decay: float = 0.999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        self.register()
    
    def register(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
    
    def update(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()
    
    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                self.backup[name] = param.data
                param.data = self.shadow[name]
    
    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.backup
                param.data = self.backup[name]
        self.backup = {}


class WarmupScheduler:
    """Learning rate warmup scheduler"""
    
    def __init__(self, optimizer, warmup_epochs: int, base_lr: float):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.base_lr = base_lr
        self.current_epoch = 0
    
    def step(self):
        if self.current_epoch < self.warmup_epochs:
            lr = self.base_lr * (self.current_epoch + 1) / self.warmup_epochs
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = lr
        self.current_epoch += 1


def mixup_data(x: torch.Tensor, y: torch.Tensor, alpha: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, float]:
    """Apply mixup augmentation"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size(0)
    index = torch.randperm(batch_size).to(x.device)
    
    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam


def cutmix_data(x: torch.Tensor, y: torch.Tensor, alpha: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, float]:
    """Apply cutmix augmentation for sequence data"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size(0)
    index = torch.randperm(batch_size).to(x.device)
    
    # For sequence data, cut along the sequence dimension
    seq_len = x.size(1)
    cut_len = int(seq_len * (1 - lam))
    cut_start = np.random.randint(0, seq_len - cut_len + 1)
    
    mixed_x = x.clone()
    mixed_x[:, cut_start:cut_start + cut_len] = x[index, cut_start:cut_start + cut_len]
    
    y_a, y_b = y, y[index]
    lam = 1 - cut_len / seq_len  # Adjust lambda based on actual cut ratio
    
    return mixed_x, y_a, y_b, lam


def get_advanced_optimizer(model: nn.Module, config: Dict[str, Any]) -> torch.optim.Optimizer:
    """Get advanced optimizer based on configuration"""
    optimizer_name = config.get('optimizer', 'adamw').lower()
    lr = config['learning_rate']
    weight_decay = config.get('weight_decay', 0.01)
    
    if optimizer_name == 'adamw':
        return optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay, betas=(0.9, 0.999))
    elif optimizer_name == 'adam':
        return optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif optimizer_name == 'sgd':
        return optim.SGD(model.parameters(), lr=lr, weight_decay=weight_decay, momentum=0.9, nesterov=True)
    elif optimizer_name == 'ranger':
        # This would require torch-optimizer package
        try:
            import torch_optimizer as optim_advanced
            return optim_advanced.Ranger(model.parameters(), lr=lr, weight_decay=weight_decay)
        except ImportError:
            print("torch-optimizer not available, falling back to AdamW")
            return optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    else:
        raise ValueError(f"Unknown optimizer: {optimizer_name}")


def get_advanced_scheduler(optimizer: torch.optim.Optimizer, config: Dict[str, Any]) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
    """Get advanced learning rate scheduler"""
    if not config.get('use_scheduler', False):
        return None
    
    scheduler_type = config.get('scheduler_type', 'cosine')
    epochs = config['epochs']
    
    if scheduler_type == 'cosine':
        return optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)
    elif scheduler_type == 'cosine_restarts':
        t_0 = config.get('t_0', 50)
        t_mult = config.get('t_mult', 2)
        return optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=t_0, T_mult=t_mult, eta_min=1e-6)
    elif scheduler_type == 'reduce_on_plateau':
        return optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    elif scheduler_type == 'step':
        return optim.lr_scheduler.StepLR(optimizer, step_size=epochs//3, gamma=0.1)
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")


def get_advanced_loss_function(config: Dict[str, Any]) -> nn.Module:
    """Get advanced loss function based on configuration"""
    if config.get('use_focal_loss', False):
        alpha = config.get('focal_alpha', 0.25)
        gamma = config.get('focal_gamma', 2.0)
        return FocalLoss(alpha=alpha, gamma=gamma)
    elif config.get('label_smoothing', 0) > 0:
        smoothing = config['label_smoothing']
        return LabelSmoothingLoss(num_classes=3, smoothing=smoothing)
    else:
        return nn.CrossEntropyLoss()


# Model architecture improvements
class AdvancedTransformerConfig:
    """Configuration for advanced transformer architecture"""
    
    @staticmethod
    def get_model_config(mode: str = "production") -> Dict[str, Any]:
        configs = {
            "quick": {
                "d_model": 256,
                "nhead": 8,
                "num_layers": 6,
                "dropout": 0.1,
                "use_pre_norm": True,
                "activation": "gelu"
            },
            "production": {
                "d_model": 512,
                "nhead": 16,
                "num_layers": 8,
                "dropout": 0.1,
                "use_pre_norm": True,
                "activation": "gelu",
                "use_attention_pooling": True
            },
            "research": {
                "d_model": 768,
                "nhead": 24,
                "num_layers": 12,
                "dropout": 0.1,
                "use_pre_norm": True,
                "activation": "gelu",
                "use_attention_pooling": True,
                "use_relative_position": True
            }
        }
        return configs.get(mode, configs["production"])
