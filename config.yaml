# Forex AI Signal System Configuration

# Data Sources Configuration
data_sources:
  primary: "yfinance"  # Free source
  secondary: "alpha_vantage"  # Free tier: 5 calls/min, 500 calls/day
  backup: "twelvedata"  # Free tier: 800 calls/day
  
  # API Keys (set in .env file)
  alpha_vantage_key: "${ALPHA_VANTAGE_API_KEY}"
  twelvedata_key: "${TWELVEDATA_API_KEY}"

# Trading Configuration
trading:
  symbol: "XAUUSD"
  timeframes:
    - "15m"  # Entry signals
    - "1h"   # Momentum detection
    - "4h"   # Trend analysis
  
  # Historical data collection
  historical_data:
    years: 5
    start_date: "2019-01-01"
    
  # Signal parameters
  signals:
    min_confidence: 0.75
    max_signals_per_day: 10
    risk_reward_ratio: 3.0
    stop_loss_pips: 30
    take_profit_pips: 90

# AI Model Configuration
ai_models:
  primary_model: "transformer"
  ensemble_models:
    - "transformer"
    - "cnn_lstm"
    - "xgboost"
  
  # Model parameters
  transformer:
    sequence_length: 100
    d_model: 256
    nhead: 8
    num_layers: 6
    dropout: 0.1
    
  cnn_lstm:
    cnn_filters: [32, 64, 128]
    lstm_units: 128
    dropout: 0.2
    
  training:
    batch_size: 32
    epochs: 100
    learning_rate: 0.001
    validation_split: 0.2
    early_stopping_patience: 10

# Feature Engineering
features:
  technical_indicators:
    # Moving Averages
    - "ema_20"
    - "ema_50"
    - "ema_200"
    - "sma_20"
    - "sma_50"
    
    # Oscillators
    - "rsi_14"
    - "macd"
    - "stoch"
    - "williams_r"
    
    # Volatility
    - "atr_14"
    - "bollinger_bands"
    - "keltner_channels"
    
    # Volume
    - "volume_sma"
    - "volume_ratio"
    
    # Price Action
    - "higher_highs"
    - "lower_lows"
    - "support_resistance"
    
  # Candlestick patterns
  candlestick_patterns:
    - "doji"
    - "hammer"
    - "engulfing"
    - "shooting_star"
    - "morning_star"
    - "evening_star"

# Database Configuration
database:
  type: "sqlite"  # Can be changed to postgresql for production
  path: "data/forex_ai.db"
  
  # Tables
  tables:
    - "historical_data"
    - "signals"
    - "trades"
    - "model_performance"
    - "feature_importance"

# Telegram Bot Configuration
telegram:
  enabled: true
  bot_token: "${TELEGRAM_BOT_TOKEN}"
  chat_id: "${TELEGRAM_CHAT_ID}"
  
  # Message formatting
  messages:
    signal_template: |
      🚨 *XAUUSD Signal Alert* 🚨
      
      📊 *Direction:* {direction}
      📈 *Confidence:* {confidence:.2%}
      💰 *Entry:* {entry_price}
      🛑 *Stop Loss:* {stop_loss}
      🎯 *Take Profit:* {take_profit}
      
      📋 *Analysis:*
      • H4 Trend: {h4_trend}
      • H1 Momentum: {h1_momentum}
      • 15M Pattern: {m15_pattern}
      
      ⏰ *Time:* {timestamp}

# Web Dashboard Configuration
dashboard:
  enabled: true
  host: "0.0.0.0"
  port: 8000
  debug: false
  
  # Dashboard features
  features:
    - "live_charts"
    - "signal_history"
    - "model_performance"
    - "trade_analytics"

# Backtesting Configuration
backtesting:
  initial_capital: 10000
  commission: 0.0001  # 0.01%
  slippage: 0.0001
  
  # Performance metrics
  metrics:
    - "total_return"
    - "sharpe_ratio"
    - "max_drawdown"
    - "win_rate"
    - "profit_factor"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  
  # Log files
  files:
    main: "logs/forex_ai.log"
    signals: "logs/signals.log"
    trades: "logs/trades.log"
    errors: "logs/errors.log"

# AWS Deployment Configuration
aws:
  region: "us-east-1"
  instance_type: "t3.medium"
  
  # Docker configuration
  docker:
    image_name: "forex-ai-system"
    container_port: 8000
    
  # Auto-scaling
  scaling:
    min_instances: 1
    max_instances: 3
    target_cpu_utilization: 70

# Monitoring & Alerts
monitoring:
  enabled: true
  
  # Health checks
  health_checks:
    data_feed: 300  # seconds
    model_prediction: 60
    signal_generation: 120
    
  # Alert thresholds
  alerts:
    max_consecutive_losses: 5
    max_drawdown_percent: 10
    min_daily_signals: 1
