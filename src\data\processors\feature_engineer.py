"""
Advanced feature engineering for Forex AI System
Includes technical indicators, price patterns, and market microstructure features
"""

import numpy as np
import pandas as pd
import ta
from typing import Dict, List, Tuple, Optional
from loguru import logger

from ...utils.config import Config


class FeatureEngineer:
    """Advanced feature engineering for forex data"""
    
    def __init__(self, config: Config):
        """Initialize feature engineer"""
        self.config = config
        self.features_config = config.features
        logger.info("Feature engineer initialized")
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive technical indicators"""
        df = data.copy()
        
        try:
            # Moving Averages
            df['ema_20'] = ta.trend.EMAIndicator(df['close'], window=20).ema_indicator()
            df['ema_50'] = ta.trend.EMAIndicator(df['close'], window=50).ema_indicator()
            df['ema_200'] = ta.trend.EMAIndicator(df['close'], window=200).ema_indicator()
            df['sma_20'] = ta.trend.SMAIndicator(df['close'], window=20).sma_indicator()
            df['sma_50'] = ta.trend.SMAIndicator(df['close'], window=50).sma_indicator()
            
            # EMA slopes (trend strength)
            df['ema_20_slope'] = df['ema_20'].diff(5) / 5
            df['ema_50_slope'] = df['ema_50'].diff(5) / 5
            df['ema_200_slope'] = df['ema_200'].diff(5) / 5
            
            # EMA distances (trend alignment)
            df['ema_20_distance'] = (df['close'] - df['ema_20']) / df['close'] * 100
            df['ema_50_distance'] = (df['close'] - df['ema_50']) / df['close'] * 100
            df['ema_200_distance'] = (df['close'] - df['ema_200']) / df['close'] * 100
            
            # Oscillators
            df['rsi_14'] = ta.momentum.RSIIndicator(df['close'], window=14).rsi()
            df['rsi_7'] = ta.momentum.RSIIndicator(df['close'], window=7).rsi()
            df['rsi_21'] = ta.momentum.RSIIndicator(df['close'], window=21).rsi()
            
            # MACD
            macd = ta.trend.MACD(df['close'])
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()
            df['macd_histogram'] = macd.macd_diff()
            
            # Stochastic
            stoch = ta.momentum.StochasticOscillator(df['high'], df['low'], df['close'])
            df['stoch_k'] = stoch.stoch()
            df['stoch_d'] = stoch.stoch_signal()
            
            # Williams %R
            df['williams_r'] = ta.momentum.WilliamsRIndicator(df['high'], df['low'], df['close']).williams_r()
            
            # Volatility Indicators
            df['atr_14'] = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=14).average_true_range()
            df['atr_7'] = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=7).average_true_range()
            
            # Bollinger Bands
            bb = ta.volatility.BollingerBands(df['close'])
            df['bb_upper'] = bb.bollinger_hband()
            df['bb_middle'] = bb.bollinger_mavg()
            df['bb_lower'] = bb.bollinger_lband()
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle'] * 100
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Keltner Channels
            kc = ta.volatility.KeltnerChannel(df['high'], df['low'], df['close'])
            df['kc_upper'] = kc.keltner_channel_hband()
            df['kc_middle'] = kc.keltner_channel_mband()
            df['kc_lower'] = kc.keltner_channel_lband()
            
            # Volume indicators (if volume data available)
            if 'volume' in df.columns and df['volume'].sum() > 0:
                df['volume_sma'] = ta.volume.VolumeSMAIndicator(df['close'], df['volume']).volume_sma()
                df['volume_ratio'] = df['volume'] / df['volume'].rolling(20).mean()
                df['vwap'] = ta.volume.VolumePriceTrendIndicator(df['close'], df['volume']).volume_price_trend()
            else:
                df['volume_sma'] = 0
                df['volume_ratio'] = 1
                df['vwap'] = df['close']
            
            # Momentum indicators
            df['roc_10'] = ta.momentum.ROCIndicator(df['close'], window=10).roc()
            df['roc_20'] = ta.momentum.ROCIndicator(df['close'], window=20).roc()
            
            # Commodity Channel Index
            df['cci'] = ta.trend.CCIIndicator(df['high'], df['low'], df['close']).cci()
            
            logger.info("Technical indicators calculated successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return df
    
    def calculate_price_action_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate price action and market structure features"""
        df = data.copy()
        
        try:
            # Candle body and shadow analysis
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
            df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
            df['total_range'] = df['high'] - df['low']
            
            # Relative sizes
            df['body_ratio'] = df['body_size'] / df['total_range']
            df['upper_shadow_ratio'] = df['upper_shadow'] / df['total_range']
            df['lower_shadow_ratio'] = df['lower_shadow'] / df['total_range']
            
            # Higher highs and lower lows
            df['higher_high'] = (df['high'] > df['high'].shift(1)).astype(int)
            df['lower_low'] = (df['low'] < df['low'].shift(1)).astype(int)
            df['higher_low'] = (df['low'] > df['low'].shift(1)).astype(int)
            df['lower_high'] = (df['high'] < df['high'].shift(1)).astype(int)
            
            # Trend structure
            df['hh_count'] = df['higher_high'].rolling(10).sum()
            df['ll_count'] = df['lower_low'].rolling(10).sum()
            df['hl_count'] = df['higher_low'].rolling(10).sum()
            df['lh_count'] = df['lower_high'].rolling(10).sum()
            
            # Price gaps
            df['gap_up'] = (df['low'] > df['high'].shift(1)).astype(int)
            df['gap_down'] = (df['high'] < df['low'].shift(1)).astype(int)
            
            # Support and resistance levels
            df = self._calculate_support_resistance(df)
            
            logger.info("Price action features calculated successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating price action features: {e}")
            return df
    
    def _calculate_support_resistance(self, data: pd.DataFrame, window: int = 20) -> pd.DataFrame:
        """Calculate dynamic support and resistance levels"""
        df = data.copy()
        
        try:
            # Rolling support and resistance
            df['resistance'] = df['high'].rolling(window).max()
            df['support'] = df['low'].rolling(window).min()
            
            # Distance to support/resistance
            df['resistance_distance'] = (df['resistance'] - df['close']) / df['close'] * 100
            df['support_distance'] = (df['close'] - df['support']) / df['close'] * 100
            
            # Pivot points (simplified)
            df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
            df['r1'] = 2 * df['pivot'] - df['low']
            df['s1'] = 2 * df['pivot'] - df['high']
            df['r2'] = df['pivot'] + (df['high'] - df['low'])
            df['s2'] = df['pivot'] - (df['high'] - df['low'])
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance: {e}")
            return df
    
    def detect_candlestick_patterns(self, data: pd.DataFrame) -> pd.DataFrame:
        """Detect candlestick patterns"""
        df = data.copy()
        
        try:
            # Doji patterns
            df['doji'] = (abs(df['close'] - df['open']) <= (df['high'] - df['low']) * 0.1).astype(int)
            
            # Hammer and hanging man
            body_size = abs(df['close'] - df['open'])
            lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
            upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
            
            df['hammer'] = (
                (lower_shadow > 2 * body_size) & 
                (upper_shadow < body_size * 0.5) &
                (df['close'] > df['open'])
            ).astype(int)
            
            df['hanging_man'] = (
                (lower_shadow > 2 * body_size) & 
                (upper_shadow < body_size * 0.5) &
                (df['close'] < df['open'])
            ).astype(int)
            
            # Shooting star
            df['shooting_star'] = (
                (upper_shadow > 2 * body_size) & 
                (lower_shadow < body_size * 0.5) &
                (df['close'] < df['open'])
            ).astype(int)
            
            # Engulfing patterns
            df['bullish_engulfing'] = (
                (df['close'] > df['open']) &
                (df['close'].shift(1) < df['open'].shift(1)) &
                (df['open'] < df['close'].shift(1)) &
                (df['close'] > df['open'].shift(1))
            ).astype(int)
            
            df['bearish_engulfing'] = (
                (df['close'] < df['open']) &
                (df['close'].shift(1) > df['open'].shift(1)) &
                (df['open'] > df['close'].shift(1)) &
                (df['close'] < df['open'].shift(1))
            ).astype(int)
            
            # Morning and evening star (simplified)
            df['morning_star'] = (
                (df['close'].shift(2) < df['open'].shift(2)) &  # First candle bearish
                (abs(df['close'].shift(1) - df['open'].shift(1)) < body_size.shift(1) * 0.3) &  # Second candle small
                (df['close'] > df['open']) &  # Third candle bullish
                (df['close'] > (df['open'].shift(2) + df['close'].shift(2)) / 2)  # Third closes above midpoint of first
            ).astype(int)
            
            df['evening_star'] = (
                (df['close'].shift(2) > df['open'].shift(2)) &  # First candle bullish
                (abs(df['close'].shift(1) - df['open'].shift(1)) < body_size.shift(1) * 0.3) &  # Second candle small
                (df['close'] < df['open']) &  # Third candle bearish
                (df['close'] < (df['open'].shift(2) + df['close'].shift(2)) / 2)  # Third closes below midpoint of first
            ).astype(int)
            
            logger.info("Candlestick patterns detected successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error detecting candlestick patterns: {e}")
            return df
    
    def calculate_market_regime_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate market regime and volatility features"""
        df = data.copy()
        
        try:
            # Volatility regimes
            df['volatility_regime'] = pd.cut(
                df['atr_14'] / df['close'] * 100,
                bins=[0, 0.5, 1.0, 2.0, float('inf')],
                labels=['low', 'normal', 'high', 'extreme']
            )
            
            # Trend strength
            ema_alignment = (
                (df['ema_20'] > df['ema_50']).astype(int) +
                (df['ema_50'] > df['ema_200']).astype(int) +
                (df['close'] > df['ema_20']).astype(int)
            )
            df['trend_strength'] = ema_alignment / 3
            
            # Market phase detection
            df['market_phase'] = 'sideways'
            df.loc[df['trend_strength'] > 0.66, 'market_phase'] = 'uptrend'
            df.loc[df['trend_strength'] < 0.33, 'market_phase'] = 'downtrend'
            
            # Momentum divergence
            price_momentum = df['close'].pct_change(10)
            rsi_momentum = df['rsi_14'].diff(10)
            df['momentum_divergence'] = np.where(
                (price_momentum > 0) & (rsi_momentum < 0), -1,
                np.where((price_momentum < 0) & (rsi_momentum > 0), 1, 0)
            )
            
            logger.info("Market regime features calculated successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating market regime features: {e}")
            return df
    
    def create_multi_timeframe_features(self, data_15m: pd.DataFrame, data_1h: pd.DataFrame, data_4h: pd.DataFrame) -> pd.DataFrame:
        """Create multi-timeframe features by combining different timeframe data"""
        try:
            # Use 15m as base timeframe
            df = data_15m.copy()
            
            # Resample higher timeframe data to 15m frequency
            # For simplicity, we'll use forward fill for higher timeframe values
            
            # Add 1h features with prefix
            for col in ['ema_20', 'ema_50', 'rsi_14', 'macd', 'atr_14', 'trend_strength']:
                if col in data_1h.columns:
                    # Simple approach: repeat 1h values for each 15m period
                    df[f'h1_{col}'] = data_1h[col].iloc[-1] if not data_1h.empty else 0
            
            # Add 4h features with prefix
            for col in ['ema_20', 'ema_50', 'ema_200', 'rsi_14', 'trend_strength', 'market_phase']:
                if col in data_4h.columns:
                    df[f'h4_{col}'] = data_4h[col].iloc[-1] if not data_4h.empty else 0
            
            # Cross-timeframe signals
            if not data_1h.empty and not data_4h.empty:
                # Trend alignment across timeframes
                df['trend_alignment'] = (
                    (df['trend_strength'] > 0.5).astype(int) +
                    (data_1h['trend_strength'].iloc[-1] > 0.5 if 'trend_strength' in data_1h.columns else 0) +
                    (data_4h['trend_strength'].iloc[-1] > 0.5 if 'trend_strength' in data_4h.columns else 0)
                ) / 3
                
                # Multi-timeframe RSI
                df['rsi_alignment'] = (
                    (df['rsi_14'] > 50).astype(int) +
                    (data_1h['rsi_14'].iloc[-1] > 50 if 'rsi_14' in data_1h.columns else 0) +
                    (data_4h['rsi_14'].iloc[-1] > 50 if 'rsi_14' in data_4h.columns else 0)
                ) / 3
            
            logger.info("Multi-timeframe features created successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error creating multi-timeframe features: {e}")
            return data_15m
    
    def engineer_features(self, data_15m: pd.DataFrame, data_1h: pd.DataFrame = None, data_4h: pd.DataFrame = None) -> pd.DataFrame:
        """Main feature engineering pipeline"""
        logger.info("Starting feature engineering pipeline")
        
        try:
            # Calculate features for 15m data
            df = self.calculate_technical_indicators(data_15m)
            df = self.calculate_price_action_features(df)
            df = self.detect_candlestick_patterns(df)
            df = self.calculate_market_regime_features(df)
            
            # Add multi-timeframe features if higher timeframe data is available
            if data_1h is not None and data_4h is not None:
                # Process higher timeframe data
                data_1h_processed = self.calculate_technical_indicators(data_1h)
                data_1h_processed = self.calculate_market_regime_features(data_1h_processed)
                
                data_4h_processed = self.calculate_technical_indicators(data_4h)
                data_4h_processed = self.calculate_market_regime_features(data_4h_processed)
                
                # Combine multi-timeframe features
                df = self.create_multi_timeframe_features(df, data_1h_processed, data_4h_processed)
            
            # Remove rows with NaN values (from indicator calculations)
            initial_rows = len(df)
            df = df.dropna()
            final_rows = len(df)
            
            if initial_rows > final_rows:
                logger.info(f"Removed {initial_rows - final_rows} rows with NaN values")
            
            logger.info(f"Feature engineering completed. Final dataset shape: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"Error in feature engineering pipeline: {e}")
            return data_15m
