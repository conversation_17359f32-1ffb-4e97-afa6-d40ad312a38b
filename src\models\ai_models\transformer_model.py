"""
Transformer-based model for forex signal prediction
Uses attention mechanism to capture long-range dependencies in price data
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Tuple, Dict, Any, Optional
from loguru import logger

from ...utils.config import Config


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class ForexTransformer(nn.Module):
    """Transformer model for forex signal prediction"""
    
    def __init__(self, config: Config):
        super().__init__()
        
        self.config = config
        transformer_config = config.ai_models.transformer
        
        self.d_model = transformer_config.d_model
        self.nhead = transformer_config.nhead
        self.num_layers = transformer_config.num_layers
        self.dropout = transformer_config.dropout
        self.sequence_length = transformer_config.sequence_length
        
        # Input projection layer
        self.input_projection = nn.Linear(1, self.d_model)  # Will be adjusted based on feature count
        
        # Positional encoding
        self.pos_encoder = PositionalEncoding(self.d_model)
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.d_model,
            nhead=self.nhead,
            dim_feedforward=self.d_model * 4,
            dropout=self.dropout,
            activation='relu',
            batch_first=True
        )
        
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=self.num_layers
        )
        
        # Output layers
        self.classifier = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.d_model // 2, self.d_model // 4),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.d_model // 4, 3)  # 3 classes: BUY, SELL, HOLD
        )
        
        # Confidence head
        self.confidence_head = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.d_model // 2, 1),
            nn.Sigmoid()
        )
        
        logger.info(f"Transformer model initialized with {self.count_parameters()} parameters")
    
    def count_parameters(self) -> int:
        """Count total number of trainable parameters"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, sequence_length, features)
            mask: Optional attention mask
            
        Returns:
            Tuple of (predictions, confidence)
        """
        batch_size, seq_len, features = x.shape
        
        # Adjust input projection if needed
        if self.input_projection.in_features != features:
            self.input_projection = nn.Linear(features, self.d_model).to(x.device)
        
        # Project input to model dimension
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # Add positional encoding
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # Apply transformer encoder
        encoded = self.transformer_encoder(x, src_key_padding_mask=mask)
        
        # Use the last token for prediction (or mean pooling)
        # Here we use mean pooling across the sequence
        pooled = encoded.mean(dim=1)  # (batch_size, d_model)
        
        # Generate predictions and confidence
        predictions = self.classifier(pooled)  # (batch_size, 3)
        confidence = self.confidence_head(pooled)  # (batch_size, 1)
        
        return predictions, confidence.squeeze(-1)
    
    def predict(self, x: torch.Tensor) -> Dict[str, Any]:
        """
        Make prediction with post-processing
        
        Args:
            x: Input tensor
            
        Returns:
            Dictionary with prediction results
        """
        self.eval()
        with torch.no_grad():
            predictions, confidence = self.forward(x)
            
            # Apply softmax to get probabilities
            probabilities = F.softmax(predictions, dim=-1)
            
            # Get predicted class
            predicted_class = torch.argmax(probabilities, dim=-1)
            
            # Convert to signal format
            class_to_signal = {0: 'HOLD', 1: 'BUY', 2: 'SELL'}
            signals = [class_to_signal[cls.item()] for cls in predicted_class]
            
            return {
                'signals': signals,
                'probabilities': probabilities.cpu().numpy(),
                'confidence': confidence.cpu().numpy(),
                'raw_predictions': predictions.cpu().numpy()
            }


class TransformerDataProcessor:
    """Data processor for transformer model"""
    
    def __init__(self, config: Config):
        self.config = config
        self.sequence_length = config.ai_models.transformer.sequence_length
        self.feature_columns = None
        self.scaler = None
        
    def prepare_sequences(self, data: pd.DataFrame, target_column: str = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare sequences for transformer training/prediction
        
        Args:
            data: DataFrame with features
            target_column: Target column name for training
            
        Returns:
            Tuple of (sequences, targets)
        """
        # Select feature columns (exclude non-numeric columns)
        numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Remove target column from features if present
        if target_column and target_column in numeric_columns:
            numeric_columns.remove(target_column)
        
        # Remove timestamp if present
        if 'timestamp' in numeric_columns:
            numeric_columns.remove('timestamp')
        
        self.feature_columns = numeric_columns
        feature_data = data[numeric_columns].values
        
        # Normalize features
        from sklearn.preprocessing import StandardScaler
        if self.scaler is None:
            self.scaler = StandardScaler()
            feature_data = self.scaler.fit_transform(feature_data)
        else:
            feature_data = self.scaler.transform(feature_data)
        
        # Create sequences
        sequences = []
        targets = []
        
        for i in range(self.sequence_length, len(feature_data)):
            # Get sequence of features
            sequence = feature_data[i-self.sequence_length:i]
            sequences.append(sequence)
            
            # Get target if provided
            if target_column and target_column in data.columns:
                targets.append(data[target_column].iloc[i])
        
        sequences = np.array(sequences)
        targets = np.array(targets) if targets else None
        
        logger.info(f"Prepared {len(sequences)} sequences with shape {sequences.shape}")
        
        return sequences, targets
    
    def create_labels(self, data: pd.DataFrame, lookahead_periods: int = 5, profit_threshold: float = 0.001) -> pd.DataFrame:
        """
        Create labels for supervised learning based on future price movements
        
        Args:
            data: DataFrame with OHLC data
            lookahead_periods: Number of periods to look ahead
            profit_threshold: Minimum profit threshold for signal generation
            
        Returns:
            DataFrame with labels
        """
        df = data.copy()
        
        # Calculate future returns
        future_high = df['high'].shift(-lookahead_periods).rolling(lookahead_periods).max()
        future_low = df['low'].shift(-lookahead_periods).rolling(lookahead_periods).min()
        
        current_close = df['close']
        
        # Calculate potential profits
        buy_profit = (future_high - current_close) / current_close
        sell_profit = (current_close - future_low) / current_close
        
        # Create labels
        labels = []
        for i in range(len(df)):
            buy_p = buy_profit.iloc[i] if not pd.isna(buy_profit.iloc[i]) else 0
            sell_p = sell_profit.iloc[i] if not pd.isna(sell_profit.iloc[i]) else 0
            
            if buy_p > profit_threshold and buy_p > sell_p:
                labels.append(1)  # BUY
            elif sell_p > profit_threshold and sell_p > buy_p:
                labels.append(2)  # SELL
            else:
                labels.append(0)  # HOLD
        
        df['label'] = labels
        df['buy_profit'] = buy_profit
        df['sell_profit'] = sell_profit
        
        return df
    
    def prepare_training_data(self, data: pd.DataFrame) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Prepare data for training
        
        Args:
            data: DataFrame with features and labels
            
        Returns:
            Tuple of (input_tensor, target_tensor)
        """
        # Create labels if not present
        if 'label' not in data.columns:
            data = self.create_labels(data)
        
        # Prepare sequences
        sequences, targets = self.prepare_sequences(data, 'label')
        
        if sequences is None or targets is None:
            raise ValueError("Failed to prepare training sequences")
        
        # Convert to tensors
        input_tensor = torch.FloatTensor(sequences)
        target_tensor = torch.LongTensor(targets)
        
        return input_tensor, target_tensor
    
    def prepare_prediction_data(self, data: pd.DataFrame) -> torch.Tensor:
        """
        Prepare data for prediction
        
        Args:
            data: DataFrame with features
            
        Returns:
            Input tensor for prediction
        """
        sequences, _ = self.prepare_sequences(data)
        
        if sequences is None:
            raise ValueError("Failed to prepare prediction sequences")
        
        # Take only the last sequence for prediction
        last_sequence = sequences[-1:] if len(sequences) > 0 else sequences
        
        return torch.FloatTensor(last_sequence)
