#!/usr/bin/env python3
"""
AWS deployment script for Forex AI Signal System
Automates EC2 instance creation and application deployment
"""

import boto3
import time
import json
import base64
from typing import Dict, Any, Optional
from pathlib import Path
import argparse
from loguru import logger


class AWSDeployer:
    """AWS deployment manager for Forex AI System"""
    
    def __init__(self, region: str = "us-east-1"):
        """Initialize AWS deployer"""
        self.region = region
        self.ec2 = boto3.client('ec2', region_name=region)
        self.ec2_resource = boto3.resource('ec2', region_name=region)
        
        logger.info(f"AWS deployer initialized for region: {region}")
    
    def create_security_group(self, group_name: str = "forex-ai-sg") -> str:
        """Create security group for the application"""
        try:
            # Check if security group already exists
            try:
                response = self.ec2.describe_security_groups(
                    GroupNames=[group_name]
                )
                sg_id = response['SecurityGroups'][0]['GroupId']
                logger.info(f"Using existing security group: {sg_id}")
                return sg_id
            except self.ec2.exceptions.ClientError:
                pass
            
            # Create new security group
            response = self.ec2.create_security_group(
                GroupName=group_name,
                Description='Security group for Forex AI Signal System'
            )
            
            sg_id = response['GroupId']
            
            # Add inbound rules
            self.ec2.authorize_security_group_ingress(
                GroupId=sg_id,
                IpPermissions=[
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 22,
                        'ToPort': 22,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'SSH access'}]
                    },
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 80,
                        'ToPort': 80,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'HTTP access'}]
                    },
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 443,
                        'ToPort': 443,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'HTTPS access'}]
                    },
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 8000,
                        'ToPort': 8000,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'Application port'}]
                    }
                ]
            )
            
            logger.info(f"Created security group: {sg_id}")
            return sg_id
            
        except Exception as e:
            logger.error(f"Error creating security group: {e}")
            raise
    
    def create_key_pair(self, key_name: str = "forex-ai-key") -> str:
        """Create EC2 key pair"""
        try:
            # Check if key pair already exists
            try:
                self.ec2.describe_key_pairs(KeyNames=[key_name])
                logger.info(f"Using existing key pair: {key_name}")
                return key_name
            except self.ec2.exceptions.ClientError:
                pass
            
            # Create new key pair
            response = self.ec2.create_key_pair(KeyName=key_name)
            
            # Save private key to file
            key_file = Path(f"{key_name}.pem")
            with open(key_file, 'w') as f:
                f.write(response['KeyMaterial'])
            
            # Set proper permissions
            key_file.chmod(0o600)
            
            logger.info(f"Created key pair: {key_name}")
            logger.info(f"Private key saved to: {key_file}")
            
            return key_name
            
        except Exception as e:
            logger.error(f"Error creating key pair: {e}")
            raise
    
    def get_user_data_script(self) -> str:
        """Generate user data script for EC2 instance"""
        return """#!/bin/bash
# Update system
yum update -y

# Install Docker
amazon-linux-extras install docker -y
systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install Git
yum install git -y

# Create application directory
mkdir -p /opt/forex-ai
cd /opt/forex-ai

# Clone repository (replace with your actual repository)
# git clone https://github.com/your-username/forex-ai-system.git .

# For now, we'll create a basic structure
cat > docker-compose.yml << 'EOF'
version: '3.8'
services:
  forex-ai:
    image: python:3.11-slim
    container_name: forex-ai-system
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    volumes:
      - ./app:/app
    working_dir: /app
    command: python -m http.server 8000
EOF

# Create basic app structure
mkdir -p app
cat > app/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Forex AI System</title>
</head>
<body>
    <h1>Forex AI Signal System</h1>
    <p>System is being deployed...</p>
    <p>Please upload your application code and restart the containers.</p>
</body>
</html>
EOF

# Start services
docker-compose up -d

# Setup log rotation
cat > /etc/logrotate.d/forex-ai << 'EOF'
/opt/forex-ai/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 ec2-user ec2-user
}
EOF

# Create startup script
cat > /opt/forex-ai/start.sh << 'EOF'
#!/bin/bash
cd /opt/forex-ai
docker-compose down
docker-compose up -d
EOF

chmod +x /opt/forex-ai/start.sh

# Add to crontab for auto-restart on reboot
echo "@reboot /opt/forex-ai/start.sh" | crontab -

# Install CloudWatch agent (optional)
wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm
rpm -U ./amazon-cloudwatch-agent.rpm

logger "Forex AI System deployment completed"
"""
    
    def launch_instance(self, instance_type: str = "t3.medium", 
                       key_name: str = "forex-ai-key",
                       security_group_id: str = None) -> Dict[str, Any]:
        """Launch EC2 instance"""
        try:
            # Get latest Amazon Linux 2 AMI
            response = self.ec2.describe_images(
                Owners=['amazon'],
                Filters=[
                    {'Name': 'name', 'Values': ['amzn2-ami-hvm-*-x86_64-gp2']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            )
            
            # Sort by creation date and get the latest
            images = sorted(response['Images'], key=lambda x: x['CreationDate'], reverse=True)
            ami_id = images[0]['ImageId']
            
            logger.info(f"Using AMI: {ami_id}")
            
            # Launch instance
            response = self.ec2.run_instances(
                ImageId=ami_id,
                MinCount=1,
                MaxCount=1,
                InstanceType=instance_type,
                KeyName=key_name,
                SecurityGroupIds=[security_group_id] if security_group_id else [],
                UserData=self.get_user_data_script(),
                TagSpecifications=[
                    {
                        'ResourceType': 'instance',
                        'Tags': [
                            {'Key': 'Name', 'Value': 'Forex-AI-System'},
                            {'Key': 'Project', 'Value': 'ForexAI'},
                            {'Key': 'Environment', 'Value': 'Production'}
                        ]
                    }
                ],
                IamInstanceProfile={
                    'Name': 'EC2-CloudWatch-Role'  # Create this role separately
                } if self.role_exists('EC2-CloudWatch-Role') else {},
                BlockDeviceMappings=[
                    {
                        'DeviceName': '/dev/xvda',
                        'Ebs': {
                            'VolumeSize': 20,
                            'VolumeType': 'gp3',
                            'DeleteOnTermination': True
                        }
                    }
                ]
            )
            
            instance_id = response['Instances'][0]['InstanceId']
            
            logger.info(f"Launched instance: {instance_id}")
            
            # Wait for instance to be running
            logger.info("Waiting for instance to be running...")
            waiter = self.ec2.get_waiter('instance_running')
            waiter.wait(InstanceIds=[instance_id])
            
            # Get instance details
            response = self.ec2.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            
            return {
                'instance_id': instance_id,
                'public_ip': instance.get('PublicIpAddress'),
                'private_ip': instance.get('PrivateIpAddress'),
                'public_dns': instance.get('PublicDnsName'),
                'state': instance['State']['Name']
            }
            
        except Exception as e:
            logger.error(f"Error launching instance: {e}")
            raise
    
    def role_exists(self, role_name: str) -> bool:
        """Check if IAM role exists"""
        try:
            iam = boto3.client('iam')
            iam.get_role(RoleName=role_name)
            return True
        except:
            return False
    
    def create_elastic_ip(self, instance_id: str) -> str:
        """Create and associate Elastic IP"""
        try:
            # Allocate Elastic IP
            response = self.ec2.allocate_address(Domain='vpc')
            allocation_id = response['AllocationId']
            public_ip = response['PublicIp']
            
            # Associate with instance
            self.ec2.associate_address(
                InstanceId=instance_id,
                AllocationId=allocation_id
            )
            
            logger.info(f"Allocated and associated Elastic IP: {public_ip}")
            return public_ip
            
        except Exception as e:
            logger.error(f"Error creating Elastic IP: {e}")
            raise
    
    def deploy(self, instance_type: str = "t3.medium", 
               use_elastic_ip: bool = True) -> Dict[str, Any]:
        """Complete deployment process"""
        logger.info("Starting AWS deployment...")
        
        try:
            # Create security group
            sg_id = self.create_security_group()
            
            # Create key pair
            key_name = self.create_key_pair()
            
            # Launch instance
            instance_info = self.launch_instance(
                instance_type=instance_type,
                key_name=key_name,
                security_group_id=sg_id
            )
            
            # Create Elastic IP if requested
            if use_elastic_ip:
                elastic_ip = self.create_elastic_ip(instance_info['instance_id'])
                instance_info['elastic_ip'] = elastic_ip
            
            # Wait for user data script to complete
            logger.info("Waiting for deployment to complete (this may take 5-10 minutes)...")
            time.sleep(300)  # Wait 5 minutes for user data script
            
            logger.info("Deployment completed successfully!")
            
            return {
                'success': True,
                'instance': instance_info,
                'security_group': sg_id,
                'key_name': key_name,
                'access_info': {
                    'ssh_command': f"ssh -i {key_name}.pem ec2-user@{instance_info.get('public_ip', instance_info.get('elastic_ip', 'N/A'))}",
                    'web_url': f"http://{instance_info.get('public_ip', instance_info.get('elastic_ip', 'N/A'))}:8000",
                    'api_url': f"http://{instance_info.get('public_ip', instance_info.get('elastic_ip', 'N/A'))}:8000/api"
                }
            }
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            return {'success': False, 'error': str(e)}


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description='Deploy Forex AI System to AWS')
    parser.add_argument('--region', default='us-east-1', help='AWS region')
    parser.add_argument('--instance-type', default='t3.medium', help='EC2 instance type')
    parser.add_argument('--no-elastic-ip', action='store_true', help='Skip Elastic IP creation')
    
    args = parser.parse_args()
    
    # Initialize deployer
    deployer = AWSDeployer(region=args.region)
    
    # Deploy
    result = deployer.deploy(
        instance_type=args.instance_type,
        use_elastic_ip=not args.no_elastic_ip
    )
    
    if result['success']:
        print("\n🎉 Deployment Successful!")
        print(f"Instance ID: {result['instance']['instance_id']}")
        print(f"Public IP: {result['instance'].get('public_ip', 'N/A')}")
        print(f"SSH Command: {result['access_info']['ssh_command']}")
        print(f"Web Dashboard: {result['access_info']['web_url']}")
        print(f"API Endpoint: {result['access_info']['api_url']}")
        print("\nNext steps:")
        print("1. SSH into the instance and upload your application code")
        print("2. Configure environment variables in .env file")
        print("3. Restart the application: docker-compose restart")
    else:
        print(f"\n❌ Deployment Failed: {result['error']}")


if __name__ == "__main__":
    main()
