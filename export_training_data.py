"""
Export training data from local database to CSV files for Google Colab training
"""

import asyncio
import pandas as pd
import json
import os
from pathlib import Path
from loguru import logger
import yaml

# Import your existing modules
from src.utils.config import Config
from src.utils.database import DatabaseManager
from src.data.processors.feature_engineer import FeatureEngineer


class DataExporter:
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize data exporter"""
        self.config = Config(config_path)
        self.db_manager = DatabaseManager(self.config)
        self.feature_engineer = FeatureEngineer(self.config)
        
        # Create export directory
        self.export_dir = Path("colab_export")
        self.export_dir.mkdir(exist_ok=True)
        
        logger.info("Data exporter initialized")
    
    async def export_historical_data(self):
        """Export historical data for all timeframes"""
        try:
            logger.info("Starting data export...")
            
            # Get data for all timeframes
            data_15m = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "15m"
            )
            data_1h = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "1h"
            )
            data_4h = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "4h"
            )
            
            if data_15m.empty:
                raise ValueError("No 15m data available for export")
            
            logger.info(f"Loaded data - 15m: {len(data_15m)}, 1h: {len(data_1h)}, 4h: {len(data_4h)}")
            
            # Engineer features for training
            featured_data = self.feature_engineer.engineer_features(
                data_15m, 
                data_1h if not data_1h.empty else None, 
                data_4h if not data_4h.empty else None
            )
            
            logger.info(f"Feature engineering completed. Shape: {featured_data.shape}")
            
            # Export to CSV files
            export_files = {}
            
            # Main training data (15m with features)
            training_file = self.export_dir / "training_data_15m.csv"
            featured_data.to_csv(training_file, index=False)
            export_files['training_data'] = str(training_file)
            logger.info(f"Exported training data: {training_file}")
            
            # Higher timeframe data for context
            if not data_1h.empty:
                h1_file = self.export_dir / "training_data_1h.csv"
                data_1h.to_csv(h1_file, index=False)
                export_files['data_1h'] = str(h1_file)
                logger.info(f"Exported 1h data: {h1_file}")
            
            if not data_4h.empty:
                h4_file = self.export_dir / "training_data_4h.csv"
                data_4h.to_csv(h4_file, index=False)
                export_files['data_4h'] = str(h4_file)
                logger.info(f"Exported 4h data: {h4_file}")
            
            return export_files, featured_data
            
        except Exception as e:
            logger.error(f"Error exporting historical data: {e}")
            raise
    
    def export_model_config(self):
        """Export model configuration for Colab"""
        try:
            # Extract relevant config sections
            colab_config = {
                'ai_models': {
                    'primary_model': self.config.ai_models.primary_model,
                    'ensemble_models': self.config.ai_models.ensemble_models,
                    'transformer': {
                        'sequence_length': self.config.ai_models.transformer.sequence_length,
                        'd_model': self.config.ai_models.transformer.d_model,
                        'nhead': self.config.ai_models.transformer.nhead,
                        'num_layers': self.config.ai_models.transformer.num_layers,
                        'dropout': self.config.ai_models.transformer.dropout
                    },
                    'cnn_lstm': {
                        'cnn_filters': self.config.ai_models.cnn_lstm.cnn_filters,
                        'lstm_units': self.config.ai_models.cnn_lstm.lstm_units,
                        'dropout': self.config.ai_models.cnn_lstm.dropout
                    },
                    'training': {
                        'batch_size': self.config.ai_models.training.batch_size,
                        'epochs': self.config.ai_models.training.epochs,
                        'learning_rate': self.config.ai_models.training.learning_rate,
                        'validation_split': self.config.ai_models.training.validation_split,
                        'early_stopping_patience': self.config.ai_models.training.early_stopping_patience
                    }
                },
                'trading': {
                    'symbol': self.config.trading.symbol
                }
            }
            
            # Save config
            config_file = self.export_dir / "model_config.json"
            with open(config_file, 'w') as f:
                json.dump(colab_config, f, indent=2)
            
            logger.info(f"Exported model config: {config_file}")
            return str(config_file)
            
        except Exception as e:
            logger.error(f"Error exporting model config: {e}")
            raise
    
    def create_data_summary(self, featured_data: pd.DataFrame):
        """Create data summary for Colab reference"""
        try:
            summary = {
                'data_shape': featured_data.shape,
                'feature_columns': list(featured_data.columns),
                'date_range': {
                    'start': str(featured_data.index.min()) if hasattr(featured_data.index, 'min') else 'N/A',
                    'end': str(featured_data.index.max()) if hasattr(featured_data.index, 'max') else 'N/A'
                },
                'missing_values': featured_data.isnull().sum().to_dict(),
                'data_types': featured_data.dtypes.astype(str).to_dict()
            }
            
            # Save summary
            summary_file = self.export_dir / "data_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            logger.info(f"Created data summary: {summary_file}")
            return str(summary_file)
            
        except Exception as e:
            logger.error(f"Error creating data summary: {e}")
            return None
    
    async def export_all(self):
        """Export all data and configurations for Colab training"""
        try:
            logger.info("Starting complete data export for Google Colab...")
            
            # Export historical data
            export_files, featured_data = await self.export_historical_data()
            
            # Export model configuration
            config_file = self.export_model_config()
            export_files['config'] = config_file
            
            # Create data summary
            summary_file = self.create_data_summary(featured_data)
            if summary_file:
                export_files['summary'] = summary_file
            
            # Create instructions file
            instructions = self._create_instructions(export_files)
            instructions_file = self.export_dir / "colab_instructions.txt"
            with open(instructions_file, 'w') as f:
                f.write(instructions)
            export_files['instructions'] = str(instructions_file)
            
            logger.info("Export completed successfully!")
            logger.info(f"Files exported to: {self.export_dir}")
            logger.info("Files created:")
            for name, path in export_files.items():
                logger.info(f"  {name}: {path}")
            
            return export_files
            
        except Exception as e:
            logger.error(f"Error during complete export: {e}")
            raise
    
    def _create_instructions(self, export_files: dict) -> str:
        """Create instructions for using exported files in Colab"""
        instructions = f"""
Google Colab Training Instructions
=================================

Files exported: {len(export_files)}

1. Upload these files to Google Drive in a folder called 'ForexAI_Training':
"""
        for name, path in export_files.items():
            instructions += f"   - {os.path.basename(path)} ({name})\n"
        
        instructions += """
2. Open the colab_training_notebook.ipynb in Google Colab

3. Follow the notebook instructions to:
   - Mount Google Drive
   - Load the exported data
   - Train your models
   - Download trained models

4. After training, download the saved models and integrate them back to your local system.

Data Summary:
- Training data contains engineered features ready for ML
- Configuration matches your local setup
- All timeframes (15m, 1h, 4h) are included where available

Happy training!
"""
        return instructions


async def main():
    """Main export function"""
    try:
        exporter = DataExporter()
        export_files = await exporter.export_all()
        
        print("\n" + "="*50)
        print("DATA EXPORT COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"\nExported files location: {exporter.export_dir}")
        print("\nNext steps:")
        print("1. Upload all files in 'colab_export' folder to Google Drive")
        print("2. Open 'colab_training_notebook.ipynb' in Google Colab")
        print("3. Follow the notebook instructions to train your models")
        print("\nSee 'colab_training_guide.md' for detailed instructions.")
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        print(f"\nExport failed: {e}")
        print("Please check the logs and try again.")


if __name__ == "__main__":
    asyncio.run(main())
