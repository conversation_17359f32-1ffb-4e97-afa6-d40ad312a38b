
Google Colab Training Instructions
=================================

Files exported: 5

1. Upload these files to Google Drive in a folder called 'ForexAI_Training':
   - training_data_15m.csv (training_data)
   - training_data_1h.csv (data_1h)
   - training_data_4h.csv (data_4h)
   - model_config.json (config)
   - data_summary.json (summary)

2. Open the colab_training_notebook.ipynb in Google Colab

3. Follow the notebook instructions to:
   - Mount Google Drive
   - Load the exported data
   - Train your models
   - Download trained models

4. After training, download the saved models and integrate them back to your local system.

Data Summary:
- Training data contains engineered features ready for ML
- Configuration matches your local setup
- All timeframes (15m, 1h, 4h) are included where available

Happy training!
