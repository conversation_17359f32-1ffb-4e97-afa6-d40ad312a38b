# Google Colab Requirements for Forex AI Training
# These packages will be installed in the Colab environment

# Core ML and Data Science
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
transformers>=4.30.0
datasets>=2.12.0

# Advanced ML packages
optuna>=3.2.0
scikit-learn>=1.3.0
xgboost>=1.7.0
lightgbm>=4.0.0

# Data manipulation and analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# Technical Analysis
ta>=0.10.2
talib-binary>=0.4.24

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Progress bars and utilities
tqdm>=4.65.0
loguru>=0.7.0

# Model serialization
joblib>=1.3.0
pickle5>=0.0.12

# Google Drive integration
pydrive>=1.3.1

# Additional utilities for Colab
ipywidgets>=8.0.0
google-colab>=1.0.0
