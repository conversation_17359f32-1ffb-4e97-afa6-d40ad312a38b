"""
Multi-source data collector for Forex AI System
Supports yfinance, Alpha Vantage, and TwelveData APIs
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import yfinance as yf
import requests
from loguru import logger

from ...utils.config import Config
from ...utils.database import DatabaseManager


class DataCollector:
    """Multi-source data collector for XAUUSD"""
    
    def __init__(self, config: Config):
        """Initialize data collector"""
        self.config = config
        self.db_manager = DatabaseManager(config)
        
        # API rate limits (requests per minute)
        self.rate_limits = {
            'alpha_vantage': 5,  # 5 calls per minute
            'twelvedata': 8,     # 8 calls per minute (free tier)
            'yfinance': 60       # No official limit, but be conservative
        }
        
        # Last API call timestamps
        self.last_calls = {
            'alpha_vantage': 0,
            'twelvedata': 0,
            'yfinance': 0
        }
        
        logger.info("Data collector initialized")
    
    def _respect_rate_limit(self, source: str):
        """Respect API rate limits"""
        current_time = time.time()
        time_since_last_call = current_time - self.last_calls[source]
        min_interval = 60 / self.rate_limits[source]  # seconds between calls
        
        if time_since_last_call < min_interval:
            sleep_time = min_interval - time_since_last_call
            logger.info(f"Rate limiting {source}: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_calls[source] = time.time()
    
    def _convert_timeframe(self, timeframe: str, source: str) -> str:
        """Convert timeframe to source-specific format"""
        timeframe_mapping = {
            'yfinance': {
                '15m': '15m',
                '1h': '1h',
                '4h': '4h',
                '1d': '1d'
            },
            'alpha_vantage': {
                '15m': '15min',
                '1h': '60min',
                '4h': '4h',  # Not directly supported
                '1d': 'daily'
            },
            'twelvedata': {
                '15m': '15min',
                '1h': '1h',
                '4h': '4h',
                '1d': '1day'
            }
        }
        
        return timeframe_mapping.get(source, {}).get(timeframe, timeframe)
    
    async def collect_yfinance_data(self, symbol: str, timeframe: str, period: str = "5y") -> pd.DataFrame:
        """Collect data from yfinance (free)"""
        try:
            self._respect_rate_limit('yfinance')
            
            # Convert XAUUSD to yfinance format
            yf_symbol = "GC=F" if symbol == "XAUUSD" else symbol
            
            # Convert timeframe
            yf_timeframe = self._convert_timeframe(timeframe, 'yfinance')
            
            logger.info(f"Fetching {yf_symbol} data from yfinance ({yf_timeframe}, {period})")
            
            # Fetch data
            ticker = yf.Ticker(yf_symbol)
            data = ticker.history(period=period, interval=yf_timeframe)
            
            if data.empty:
                logger.warning(f"No data received from yfinance for {yf_symbol}")
                return pd.DataFrame()
            
            # Standardize column names
            data = data.reset_index()
            data.columns = [col.lower().replace(' ', '_') for col in data.columns]
            
            # Rename columns to standard format
            column_mapping = {
                'datetime': 'timestamp',
                'date': 'timestamp'
            }
            data = data.rename(columns=column_mapping)
            
            # Ensure required columns exist
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    if col == 'volume':
                        data[col] = 0
                    else:
                        logger.error(f"Missing required column: {col}")
                        return pd.DataFrame()
            
            # Select only required columns
            data = data[required_columns]
            
            logger.info(f"Successfully fetched {len(data)} records from yfinance")
            return data
            
        except Exception as e:
            logger.error(f"Error fetching data from yfinance: {e}")
            return pd.DataFrame()
    
    async def collect_alpha_vantage_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """Collect data from Alpha Vantage API"""
        try:
            if not self.config.data_sources.alpha_vantage_key:
                logger.warning("Alpha Vantage API key not configured")
                return pd.DataFrame()
            
            self._respect_rate_limit('alpha_vantage')
            
            # Convert timeframe
            av_timeframe = self._convert_timeframe(timeframe, 'alpha_vantage')
            
            # Alpha Vantage doesn't support XAUUSD directly, skip for now
            if symbol == "XAUUSD":
                logger.warning("Alpha Vantage doesn't support XAUUSD directly")
                return pd.DataFrame()
            
            # Determine function based on timeframe
            if av_timeframe in ['1min', '5min', '15min', '30min', '60min']:
                function = 'TIME_SERIES_INTRADAY'
                params = {
                    'function': function,
                    'symbol': symbol,
                    'interval': av_timeframe,
                    'apikey': self.config.data_sources.alpha_vantage_key,
                    'outputsize': 'full'
                }
            else:
                function = 'TIME_SERIES_DAILY'
                params = {
                    'function': function,
                    'symbol': symbol,
                    'apikey': self.config.data_sources.alpha_vantage_key,
                    'outputsize': 'full'
                }
            
            logger.info(f"Fetching {symbol} data from Alpha Vantage ({av_timeframe})")
            
            # Make API request
            url = "https://www.alphavantage.co/query"
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data_json = response.json()
            
            # Check for API errors
            if 'Error Message' in data_json:
                logger.error(f"Alpha Vantage API error: {data_json['Error Message']}")
                return pd.DataFrame()
            
            if 'Note' in data_json:
                logger.warning(f"Alpha Vantage API note: {data_json['Note']}")
                return pd.DataFrame()
            
            # Extract time series data
            time_series_key = None
            for key in data_json.keys():
                if 'Time Series' in key:
                    time_series_key = key
                    break
            
            if not time_series_key:
                logger.error("No time series data found in Alpha Vantage response")
                return pd.DataFrame()
            
            time_series = data_json[time_series_key]
            
            # Convert to DataFrame
            data = []
            for timestamp, values in time_series.items():
                data.append({
                    'timestamp': pd.to_datetime(timestamp),
                    'open': float(values['1. open']),
                    'high': float(values['2. high']),
                    'low': float(values['3. low']),
                    'close': float(values['4. close']),
                    'volume': float(values['5. volume'])
                })
            
            df = pd.DataFrame(data)
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            logger.info(f"Successfully fetched {len(df)} records from Alpha Vantage")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching data from Alpha Vantage: {e}")
            return pd.DataFrame()
    
    async def collect_twelvedata_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """Collect data from TwelveData API"""
        try:
            if not self.config.data_sources.twelvedata_key:
                logger.warning("TwelveData API key not configured")
                return pd.DataFrame()
            
            self._respect_rate_limit('twelvedata')
            
            # Convert timeframe
            td_timeframe = self._convert_timeframe(timeframe, 'twelvedata')
            
            # TwelveData symbol for Gold
            td_symbol = "XAU/USD" if symbol == "XAUUSD" else symbol
            
            logger.info(f"Fetching {td_symbol} data from TwelveData ({td_timeframe})")
            
            # API parameters
            params = {
                'symbol': td_symbol,
                'interval': td_timeframe,
                'apikey': self.config.data_sources.twelvedata_key,
                'outputsize': 5000,  # Maximum for free tier
                'format': 'JSON'
            }
            
            # Make API request
            url = "https://api.twelvedata.com/time_series"
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data_json = response.json()
            
            # Check for API errors
            if 'status' in data_json and data_json['status'] == 'error':
                logger.error(f"TwelveData API error: {data_json.get('message', 'Unknown error')}")
                return pd.DataFrame()
            
            if 'values' not in data_json:
                logger.error("No values found in TwelveData response")
                return pd.DataFrame()
            
            # Convert to DataFrame
            data = []
            for item in data_json['values']:
                data.append({
                    'timestamp': pd.to_datetime(item['datetime']),
                    'open': float(item['open']),
                    'high': float(item['high']),
                    'low': float(item['low']),
                    'close': float(item['close']),
                    'volume': float(item.get('volume', 0))
                })
            
            df = pd.DataFrame(data)
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            logger.info(f"Successfully fetched {len(df)} records from TwelveData")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching data from TwelveData: {e}")
            return pd.DataFrame()
    
    async def collect_historical_data(self, symbol: str, timeframe: str, years: int = 5) -> pd.DataFrame:
        """Collect historical data using fallback strategy"""
        logger.info(f"Collecting historical data for {symbol} {timeframe} ({years} years)")
        
        # Try primary source first
        data = await self.collect_yfinance_data(symbol, timeframe, f"{years}y")
        
        if data.empty:
            logger.warning("Primary source (yfinance) failed, trying secondary source")
            data = await self.collect_alpha_vantage_data(symbol, timeframe)
        
        if data.empty:
            logger.warning("Secondary source failed, trying backup source")
            data = await self.collect_twelvedata_data(symbol, timeframe)
        
        if data.empty:
            logger.error(f"All data sources failed for {symbol} {timeframe}")
            return pd.DataFrame()
        
        # Store in database
        try:
            await self.db_manager.insert_historical_data(data, symbol, timeframe)
            logger.info(f"Historical data stored in database for {symbol} {timeframe}")
        except Exception as e:
            logger.error(f"Error storing historical data: {e}")
        
        return data
    
    async def get_latest_data(self, symbol: str, timeframe: str, limit: int = 300) -> pd.DataFrame:
        """Get latest data for real-time analysis"""
        try:
            # First try to get from database
            data = await self.db_manager.get_historical_data(symbol, timeframe, limit)
            
            if not data.empty:
                # Check if data is recent enough (within last hour for 15m, 4 hours for 1h, etc.)
                latest_timestamp = data['timestamp'].max()
                current_time = datetime.utcnow()
                
                time_thresholds = {
                    '15m': timedelta(minutes=30),
                    '1h': timedelta(hours=2),
                    '4h': timedelta(hours=8)
                }
                
                threshold = time_thresholds.get(timeframe, timedelta(hours=1))
                
                if current_time - latest_timestamp <= threshold:
                    logger.info(f"Using cached data for {symbol} {timeframe}")
                    return data
            
            # Data is stale or missing, fetch fresh data
            logger.info(f"Fetching fresh data for {symbol} {timeframe}")
            
            # For real-time, use shorter period
            fresh_data = await self.collect_yfinance_data(symbol, timeframe, "1mo")
            
            if fresh_data.empty:
                # Fallback to cached data if available
                if not data.empty:
                    logger.warning("Fresh data unavailable, using cached data")
                    return data
                else:
                    logger.error(f"No data available for {symbol} {timeframe}")
                    return pd.DataFrame()
            
            # Update database with fresh data
            try:
                await self.db_manager.insert_historical_data(fresh_data, symbol, timeframe)
            except Exception as e:
                logger.warning(f"Error updating database with fresh data: {e}")
            
            return fresh_data.tail(limit)
            
        except Exception as e:
            logger.error(f"Error getting latest data: {e}")
            return pd.DataFrame()
