#!/usr/bin/env python3
"""
Quick database check script
"""

import sys
import asyncio
from pathlib import Path

# Add src to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.database import DatabaseManager
from src.utils.config_simple import Config

async def check_database():
    """Check database contents"""
    try:
        config = Config()
        db = DatabaseManager(config)
        
        print("🔍 Checking database contents...")
        
        # Check 15m data
        data_15m = await db.get_historical_data('XAUUSD', '15m', 5)
        print(f"📊 15m data: {len(data_15m)} records")
        if not data_15m.empty:
            print("Latest 15m records:")
            print(data_15m.tail(3))
        
        # Check 1h data
        data_1h = await db.get_historical_data('XAUUSD', '1h', 5)
        print(f"📊 1h data: {len(data_1h)} records")
        
        # Check 4h data
        data_4h = await db.get_historical_data('XAUUSD', '4h', 5)
        print(f"📊 4h data: {len(data_4h)} records")
        
        print("✅ Database check completed!")
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    asyncio.run(check_database())
