"""
Multi-timeframe signal validation for Forex AI System
Validates signals across H4, H1, and 15M timeframes
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
from loguru import logger

from ...utils.config import Config


class MultiTimeframeValidator:
    """Multi-timeframe signal validation engine"""
    
    def __init__(self, config: Config):
        """Initialize validator"""
        self.config = config
        logger.info("Multi-timeframe validator initialized")
    
    def analyze_h4_trend(self, data_4h: pd.DataFrame) -> Dict[str, Any]:
        """Analyze H4 timeframe for macro trend"""
        if data_4h.empty or len(data_4h) < 20:
            return {
                'trend': 'Unknown',
                'strength': 0.0,
                'valid': False,
                'reason': 'Insufficient H4 data'
            }
        
        try:
            # Calculate trend indicators
            latest = data_4h.iloc[-1]
            
            # EMA trend analysis
            ema_20 = latest.get('ema_20', latest['close'])
            ema_50 = latest.get('ema_50', latest['close'])
            ema_200 = latest.get('ema_200', latest['close'])
            
            # Price position relative to EMAs
            price = latest['close']
            
            # Trend determination
            if price > ema_20 > ema_50 > ema_200:
                trend = 'Strong Uptrend'
                strength = 1.0
            elif price > ema_20 > ema_50:
                trend = 'Uptrend'
                strength = 0.75
            elif price > ema_20:
                trend = 'Weak Uptrend'
                strength = 0.5
            elif price < ema_20 < ema_50 < ema_200:
                trend = 'Strong Downtrend'
                strength = 1.0
            elif price < ema_20 < ema_50:
                trend = 'Downtrend'
                strength = 0.75
            elif price < ema_20:
                trend = 'Weak Downtrend'
                strength = 0.5
            else:
                trend = 'Sideways'
                strength = 0.25
            
            # Additional trend confirmation
            rsi = latest.get('rsi_14', 50)
            macd = latest.get('macd', 0)
            
            # Trend strength adjustment based on momentum
            if trend.startswith('Up') and rsi > 50 and macd > 0:
                strength = min(1.0, strength + 0.1)
            elif trend.startswith('Down') and rsi < 50 and macd < 0:
                strength = min(1.0, strength + 0.1)
            elif trend == 'Sideways' and 40 < rsi < 60:
                strength = min(1.0, strength + 0.1)
            
            return {
                'trend': trend,
                'strength': strength,
                'valid': True,
                'ema_alignment': price > ema_20 > ema_50 > ema_200 if 'Up' in trend else price < ema_20 < ema_50 < ema_200,
                'rsi': rsi,
                'macd': macd
            }
            
        except Exception as e:
            logger.error(f"Error analyzing H4 trend: {e}")
            return {
                'trend': 'Unknown',
                'strength': 0.0,
                'valid': False,
                'reason': f'Analysis error: {e}'
            }
    
    def analyze_h1_momentum(self, data_1h: pd.DataFrame) -> Dict[str, Any]:
        """Analyze H1 timeframe for momentum and pullbacks"""
        if data_1h.empty or len(data_1h) < 10:
            return {
                'momentum': 'Unknown',
                'pullback_quality': 0.0,
                'valid': False,
                'reason': 'Insufficient H1 data'
            }
        
        try:
            latest = data_1h.iloc[-1]
            previous = data_1h.iloc[-2] if len(data_1h) > 1 else latest
            
            # MACD momentum analysis
            macd = latest.get('macd', 0)
            macd_signal = latest.get('macd_signal', 0)
            macd_histogram = latest.get('macd_histogram', 0)
            
            # RSI momentum
            rsi = latest.get('rsi_14', 50)
            rsi_prev = previous.get('rsi_14', 50)
            
            # Price momentum
            price_change = (latest['close'] - previous['close']) / previous['close'] * 100
            
            # Determine momentum
            if macd > macd_signal and macd_histogram > 0 and rsi > 50:
                momentum = 'Bullish'
                momentum_strength = 0.8
            elif macd < macd_signal and macd_histogram < 0 and rsi < 50:
                momentum = 'Bearish'
                momentum_strength = 0.8
            elif abs(price_change) < 0.1 and 45 < rsi < 55:
                momentum = 'Neutral'
                momentum_strength = 0.3
            else:
                momentum = 'Mixed'
                momentum_strength = 0.5
            
            # Pullback quality analysis
            pullback_quality = self._analyze_pullback_quality(data_1h)
            
            # Divergence detection
            divergence = self._detect_divergence(data_1h)
            
            return {
                'momentum': momentum,
                'strength': momentum_strength,
                'pullback_quality': pullback_quality,
                'divergence': divergence,
                'valid': True,
                'macd_signal': macd > macd_signal,
                'rsi': rsi,
                'price_change': price_change
            }
            
        except Exception as e:
            logger.error(f"Error analyzing H1 momentum: {e}")
            return {
                'momentum': 'Unknown',
                'pullback_quality': 0.0,
                'valid': False,
                'reason': f'Analysis error: {e}'
            }
    
    def _analyze_pullback_quality(self, data: pd.DataFrame) -> float:
        """Analyze the quality of pullbacks in the data"""
        if len(data) < 5:
            return 0.0
        
        try:
            # Look at last 5 candles for pullback pattern
            recent_data = data.tail(5)
            
            # Calculate pullback metrics
            high_low_ratio = (recent_data['high'].max() - recent_data['low'].min()) / recent_data['close'].mean()
            volume_consistency = recent_data['volume'].std() / recent_data['volume'].mean() if recent_data['volume'].sum() > 0 else 0
            
            # Fibonacci retracement levels (simplified)
            price_range = recent_data['high'].max() - recent_data['low'].min()
            current_price = recent_data['close'].iloc[-1]
            retracement_level = (current_price - recent_data['low'].min()) / price_range if price_range > 0 else 0
            
            # Quality score based on multiple factors
            quality_score = 0.0
            
            # Prefer pullbacks that retrace 38.2% to 61.8%
            if 0.382 <= retracement_level <= 0.618:
                quality_score += 0.4
            elif 0.236 <= retracement_level <= 0.786:
                quality_score += 0.2
            
            # Prefer controlled pullbacks (not too volatile)
            if high_low_ratio < 0.02:  # Less than 2% range
                quality_score += 0.3
            
            # Volume consistency (if available)
            if volume_consistency < 0.5:
                quality_score += 0.3
            
            return min(1.0, quality_score)
            
        except Exception as e:
            logger.error(f"Error analyzing pullback quality: {e}")
            return 0.0
    
    def _detect_divergence(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect price-momentum divergences"""
        if len(data) < 10:
            return {'type': 'None', 'strength': 0.0}
        
        try:
            # Get recent data
            recent_data = data.tail(10)
            
            # Price highs and lows
            price_highs = recent_data['high']
            price_lows = recent_data['low']
            
            # RSI highs and lows
            rsi_values = recent_data.get('rsi_14', pd.Series([50] * len(recent_data)))
            
            # Simple divergence detection
            price_trend = price_highs.iloc[-1] - price_highs.iloc[0]
            rsi_trend = rsi_values.iloc[-1] - rsi_values.iloc[0]
            
            # Bullish divergence: price making lower lows, RSI making higher lows
            if price_trend < 0 and rsi_trend > 0:
                return {'type': 'Bullish', 'strength': 0.7}
            
            # Bearish divergence: price making higher highs, RSI making lower highs
            elif price_trend > 0 and rsi_trend < 0:
                return {'type': 'Bearish', 'strength': 0.7}
            
            else:
                return {'type': 'None', 'strength': 0.0}
                
        except Exception as e:
            logger.error(f"Error detecting divergence: {e}")
            return {'type': 'None', 'strength': 0.0}
    
    def analyze_m15_patterns(self, data_15m: pd.DataFrame, featured_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze 15M timeframe for entry patterns"""
        if data_15m.empty or len(data_15m) < 5:
            return {
                'pattern': 'Unknown',
                'quality': 0.0,
                'valid': False,
                'reason': 'Insufficient 15M data'
            }
        
        try:
            latest = featured_data.iloc[-1] if not featured_data.empty else data_15m.iloc[-1]
            
            # Candlestick pattern analysis
            patterns = []
            pattern_strength = 0.0
            
            # Check for specific patterns in featured data
            if 'doji' in featured_data.columns and latest.get('doji', 0) == 1:
                patterns.append('Doji')
                pattern_strength += 0.3
            
            if 'hammer' in featured_data.columns and latest.get('hammer', 0) == 1:
                patterns.append('Hammer')
                pattern_strength += 0.4
            
            if 'bullish_engulfing' in featured_data.columns and latest.get('bullish_engulfing', 0) == 1:
                patterns.append('Bullish Engulfing')
                pattern_strength += 0.6
            
            if 'bearish_engulfing' in featured_data.columns and latest.get('bearish_engulfing', 0) == 1:
                patterns.append('Bearish Engulfing')
                pattern_strength += 0.6
            
            # Support/Resistance analysis
            support_resistance_quality = 0.0
            if 'support_distance' in featured_data.columns and 'resistance_distance' in featured_data.columns:
                support_dist = abs(latest.get('support_distance', 100))
                resistance_dist = abs(latest.get('resistance_distance', 100))
                
                # Quality increases when price is near support/resistance
                if support_dist < 1.0:  # Within 1% of support
                    support_resistance_quality += 0.4
                if resistance_dist < 1.0:  # Within 1% of resistance
                    support_resistance_quality += 0.4
            
            # Bollinger Bands analysis
            bb_quality = 0.0
            if 'bb_position' in featured_data.columns:
                bb_position = latest.get('bb_position', 0.5)
                # Quality increases at extreme BB positions
                if bb_position < 0.1 or bb_position > 0.9:
                    bb_quality = 0.3
            
            # Volume analysis (if available)
            volume_quality = 0.0
            if 'volume_ratio' in featured_data.columns:
                volume_ratio = latest.get('volume_ratio', 1.0)
                if volume_ratio > 1.5:  # Above average volume
                    volume_quality = 0.2
            
            # Combine all pattern qualities
            total_quality = min(1.0, pattern_strength + support_resistance_quality + bb_quality + volume_quality)
            
            # Determine main pattern
            main_pattern = patterns[0] if patterns else 'No Clear Pattern'
            if len(patterns) > 1:
                main_pattern = f"Multiple: {', '.join(patterns[:2])}"
            
            return {
                'pattern': main_pattern,
                'quality': total_quality,
                'valid': True,
                'patterns_detected': patterns,
                'support_resistance_quality': support_resistance_quality,
                'volume_quality': volume_quality
            }
            
        except Exception as e:
            logger.error(f"Error analyzing 15M patterns: {e}")
            return {
                'pattern': 'Unknown',
                'quality': 0.0,
                'valid': False,
                'reason': f'Analysis error: {e}'
            }
    
    async def validate_signal(self, signal: str, data_15m: pd.DataFrame, 
                            data_1h: pd.DataFrame, data_4h: pd.DataFrame, 
                            featured_data: pd.DataFrame) -> Dict[str, Any]:
        """Validate signal across all timeframes"""
        logger.debug(f"Validating {signal} signal across timeframes")
        
        try:
            # Analyze each timeframe
            h4_analysis = self.analyze_h4_trend(data_4h)
            h1_analysis = self.analyze_h1_momentum(data_1h)
            m15_analysis = self.analyze_m15_patterns(data_15m, featured_data)
            
            # Validation logic
            is_valid = True
            validation_score = 0.0
            reasons = []
            
            # H4 trend alignment
            if h4_analysis['valid']:
                h4_trend = h4_analysis['trend']
                if signal == 'BUY' and 'Up' in h4_trend:
                    validation_score += 0.4 * h4_analysis['strength']
                elif signal == 'SELL' and 'Down' in h4_trend:
                    validation_score += 0.4 * h4_analysis['strength']
                elif signal in ['BUY', 'SELL'] and 'Sideways' in h4_trend:
                    validation_score += 0.2  # Neutral for sideways
                else:
                    reasons.append(f"H4 trend ({h4_trend}) conflicts with {signal} signal")
                    validation_score -= 0.2
            
            # H1 momentum alignment
            if h1_analysis['valid']:
                h1_momentum = h1_analysis['momentum']
                if signal == 'BUY' and h1_momentum == 'Bullish':
                    validation_score += 0.3 * h1_analysis['strength']
                elif signal == 'SELL' and h1_momentum == 'Bearish':
                    validation_score += 0.3 * h1_analysis['strength']
                elif h1_momentum == 'Neutral':
                    validation_score += 0.1  # Slight positive for neutral
                else:
                    reasons.append(f"H1 momentum ({h1_momentum}) conflicts with {signal} signal")
                    validation_score -= 0.1
                
                # Pullback quality bonus
                validation_score += 0.1 * h1_analysis['pullback_quality']
            
            # 15M pattern quality
            if m15_analysis['valid']:
                validation_score += 0.2 * m15_analysis['quality']
                if m15_analysis['quality'] < 0.3:
                    reasons.append("Weak 15M entry pattern")
            
            # Final validation decision
            min_validation_score = 0.5  # Minimum score to pass validation
            if validation_score < min_validation_score:
                is_valid = False
                reasons.append(f"Validation score {validation_score:.2f} below threshold {min_validation_score}")
            
            # Additional safety checks
            if not any([h4_analysis['valid'], h1_analysis['valid'], m15_analysis['valid']]):
                is_valid = False
                reasons.append("No valid timeframe analysis available")
            
            return {
                'is_valid': is_valid,
                'validation_score': validation_score,
                'reason': '; '.join(reasons) if reasons else 'Signal validated successfully',
                'h4_trend': h4_analysis.get('trend', 'Unknown'),
                'h1_momentum': h1_analysis.get('momentum', 'Unknown'),
                'm15_pattern': m15_analysis.get('pattern', 'Unknown'),
                'detailed_analysis': {
                    'h4': h4_analysis,
                    'h1': h1_analysis,
                    'm15': m15_analysis
                }
            }
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return {
                'is_valid': False,
                'validation_score': 0.0,
                'reason': f'Validation error: {e}',
                'h4_trend': 'Unknown',
                'h1_momentum': 'Unknown',
                'm15_pattern': 'Unknown'
            }
