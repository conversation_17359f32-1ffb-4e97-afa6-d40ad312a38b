"""
Test script for Colab-trained models
"""

import asyncio
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from loguru import logger
import json

# Import your existing modules
from src.utils.config import Config
from src.utils.database import DatabaseManager
from src.data.processors.feature_engineer import FeatureEngineer
from src.models.ai_models.transformer_model import ForexTransformer
from src.models.ai_models.cnn_lstm_model import CNNLSTMModel


class ColabModelTester:
    """Test Colab-trained models with real data"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize model tester"""
        self.config = Config(config_path)
        self.db_manager = DatabaseManager(self.config)
        self.feature_engineer = FeatureEngineer(self.config)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"Model tester initialized - Device: {self.device}")
    
    def load_integrated_model(self, model_name: str) -> tuple:
        """Load integrated Colab model"""
        try:
            model_dir = Path(f"models/saved_models/{model_name}_colab_trained")
            
            if not model_dir.exists():
                raise FileNotFoundError(f"Integrated model directory not found: {model_dir}")
            
            # Load integration metadata
            metadata_file = model_dir / f"{model_name}_integration.json"
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            # Create model instance
            original_metadata = metadata['original_metadata']
            input_features = original_metadata['input_features']
            
            if model_name == 'transformer':
                model = ForexTransformer(self.config)
                if hasattr(model, 'input_projection'):
                    model.input_projection = torch.nn.Linear(input_features, model.d_model)
            elif model_name == 'cnn_lstm':
                model = CNNLSTMModel(self.config, input_features)
            else:
                raise ValueError(f"Unknown model type: {model_name}")
            
            # Load weights
            model_file = model_dir / f"{model_name}.pth"
            state_dict = torch.load(model_file, map_location=self.device)
            model.load_state_dict(state_dict)
            model.to(self.device)
            model.eval()
            
            logger.info(f"✅ Loaded {model_name} model from {model_dir}")
            return model, metadata
            
        except Exception as e:
            logger.error(f"Error loading {model_name} model: {e}")
            raise
    
    async def get_test_data(self, limit: int = 1000) -> pd.DataFrame:
        """Get recent data for testing"""
        try:
            # Get recent 15m data
            data_15m = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "15m", limit=limit
            )
            
            if data_15m.empty:
                raise ValueError("No test data available")
            
            # Engineer features
            featured_data = self.feature_engineer.engineer_features(data_15m)
            
            logger.info(f"✅ Prepared test data: {featured_data.shape}")
            return featured_data
            
        except Exception as e:
            logger.error(f"Error getting test data: {e}")
            raise
    
    def create_test_sequences(self, data: pd.DataFrame, sequence_length: int = 100) -> np.ndarray:
        """Create sequences for model testing"""
        try:
            # Select numeric columns
            numeric_data = data.select_dtypes(include=[np.number]).fillna(0)
            
            # Create sequences
            sequences = []
            for i in range(len(numeric_data) - sequence_length + 1):
                sequences.append(numeric_data.iloc[i:i + sequence_length].values)
            
            sequences = np.array(sequences)
            logger.info(f"✅ Created test sequences: {sequences.shape}")
            return sequences
            
        except Exception as e:
            logger.error(f"Error creating test sequences: {e}")
            raise
    
    def test_model_predictions(self, model: torch.nn.Module, sequences: np.ndarray, 
                             model_name: str) -> dict:
        """Test model predictions on real data"""
        try:
            logger.info(f"Testing {model_name} predictions...")
            
            # Convert to tensor
            sequences_tensor = torch.FloatTensor(sequences).to(self.device)
            
            predictions_list = []
            confidence_list = []
            
            # Process in batches to avoid memory issues
            batch_size = 32
            num_batches = len(sequences_tensor) // batch_size + (1 if len(sequences_tensor) % batch_size > 0 else 0)
            
            with torch.no_grad():
                for i in range(num_batches):
                    start_idx = i * batch_size
                    end_idx = min((i + 1) * batch_size, len(sequences_tensor))
                    batch = sequences_tensor[start_idx:end_idx]
                    
                    predictions, confidence = model(batch)
                    
                    # Get predicted classes
                    predicted_classes = torch.argmax(predictions, dim=1)
                    predictions_list.extend(predicted_classes.cpu().numpy())
                    confidence_list.extend(confidence.cpu().numpy())
            
            # Analyze results
            predictions_array = np.array(predictions_list)
            confidence_array = np.array(confidence_list)
            
            results = {
                'model_name': model_name,
                'total_predictions': len(predictions_array),
                'class_distribution': {
                    'hold': int(np.sum(predictions_array == 0)),
                    'buy': int(np.sum(predictions_array == 1)),
                    'sell': int(np.sum(predictions_array == 2))
                },
                'confidence_stats': {
                    'mean': float(np.mean(confidence_array)),
                    'std': float(np.std(confidence_array)),
                    'min': float(np.min(confidence_array)),
                    'max': float(np.max(confidence_array))
                },
                'high_confidence_predictions': int(np.sum(confidence_array > 0.7)),
                'low_confidence_predictions': int(np.sum(confidence_array < 0.3))
            }
            
            logger.info(f"✅ {model_name} testing completed")
            logger.info(f"  Total predictions: {results['total_predictions']}")
            logger.info(f"  Class distribution: {results['class_distribution']}")
            logger.info(f"  Average confidence: {results['confidence_stats']['mean']:.4f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error testing {model_name} predictions: {e}")
            raise
    
    async def run_comprehensive_test(self) -> dict:
        """Run comprehensive test of all Colab models"""
        try:
            logger.info("🚀 Starting comprehensive Colab model test...")
            
            # Get test data
            test_data = await self.get_test_data(limit=2000)
            
            # Find available models
            models_dir = Path("models/saved_models")
            available_models = []
            
            for model_dir in models_dir.glob("*_colab_trained"):
                model_name = model_dir.name.replace("_colab_trained", "")
                available_models.append(model_name)
            
            if not available_models:
                raise ValueError("No Colab-trained models found. Run integration first.")
            
            logger.info(f"Found models: {available_models}")
            
            test_results = {}
            
            for model_name in available_models:
                try:
                    logger.info(f"Testing {model_name}...")
                    
                    # Load model
                    model, metadata = self.load_integrated_model(model_name)
                    
                    # Get sequence length from metadata
                    sequence_length = metadata['original_metadata'].get('sequence_length', 100)
                    
                    # Create test sequences
                    sequences = self.create_test_sequences(test_data, sequence_length)
                    
                    if len(sequences) == 0:
                        logger.warning(f"No sequences created for {model_name}")
                        continue
                    
                    # Test predictions
                    results = self.test_model_predictions(model, sequences, model_name)
                    
                    # Add metadata info
                    results['metadata'] = {
                        'training_date': metadata['original_metadata'].get('training_date', 'Unknown'),
                        'training_config': metadata['original_metadata'].get('training_config', {}),
                        'final_metrics': metadata['original_metadata'].get('final_metrics', {}),
                        'sequence_length': sequence_length,
                        'input_features': metadata['original_metadata'].get('input_features', 0)
                    }
                    
                    test_results[model_name] = results
                    
                except Exception as e:
                    logger.error(f"Failed to test {model_name}: {e}")
                    test_results[model_name] = {
                        'error': str(e),
                        'status': 'failed'
                    }
            
            # Generate summary
            summary = self.generate_test_summary(test_results)
            test_results['summary'] = summary
            
            logger.info("🎉 Comprehensive test completed!")
            return test_results
            
        except Exception as e:
            logger.error(f"Error during comprehensive test: {e}")
            raise
    
    def generate_test_summary(self, test_results: dict) -> dict:
        """Generate test summary"""
        successful_tests = [name for name, result in test_results.items() 
                          if 'error' not in result]
        
        if not successful_tests:
            return {'status': 'all_failed', 'successful_models': 0}
        
        # Compare models
        best_model = None
        best_confidence = 0
        
        for model_name in successful_tests:
            result = test_results[model_name]
            avg_confidence = result['confidence_stats']['mean']
            
            if avg_confidence > best_confidence:
                best_confidence = avg_confidence
                best_model = model_name
        
        summary = {
            'status': 'success',
            'total_models_tested': len(test_results),
            'successful_models': len(successful_tests),
            'failed_models': len(test_results) - len(successful_tests),
            'best_model': best_model,
            'best_model_confidence': best_confidence,
            'models_tested': successful_tests
        }
        
        return summary
    
    def save_test_results(self, results: dict):
        """Save test results to file"""
        try:
            results_file = Path("colab_model_test_results.json")
            
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            logger.info(f"✅ Test results saved to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving test results: {e}")


async def main():
    """Main test function"""
    try:
        tester = ColabModelTester()
        results = await tester.run_comprehensive_test()
        
        # Save results
        tester.save_test_results(results)
        
        # Print summary
        print("\n" + "="*60)
        print("COLAB MODEL TEST RESULTS")
        print("="*60)
        
        summary = results.get('summary', {})
        
        if summary.get('status') == 'success':
            print(f"✅ Test Status: SUCCESS")
            print(f"📊 Models tested: {summary['successful_models']}/{summary['total_models_tested']}")
            print(f"🏆 Best model: {summary['best_model']}")
            print(f"🎯 Best confidence: {summary['best_model_confidence']:.4f}")
            
            print(f"\n📋 Individual Model Results:")
            for model_name in summary['models_tested']:
                result = results[model_name]
                print(f"\n🤖 {model_name.upper()}:")
                print(f"  Predictions: {result['total_predictions']}")
                print(f"  Avg Confidence: {result['confidence_stats']['mean']:.4f}")
                print(f"  High Confidence: {result['high_confidence_predictions']}")
                print(f"  Class Distribution: {result['class_distribution']}")
        else:
            print(f"❌ Test Status: FAILED")
            print(f"All model tests failed. Check the logs for details.")
        
        print(f"\n📁 Detailed results saved to: colab_model_test_results.json")
        print(f"🚀 Your Colab models are ready for production use!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        logger.error(f"Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
