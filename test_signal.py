#!/usr/bin/env python3
"""
Test signal generation and Telegram notification
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime

# Add src to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.database import DatabaseManager
from src.utils.config_simple import Config
from src.signals.delivery.telegram_bot import TelegramBot

async def test_signal_system():
    """Test the signal generation and notification system"""
    try:
        config = Config()
        db = DatabaseManager(config)
        
        print("🧪 Testing Signal System...")
        
        # Create a test signal
        test_signal = {
            'symbol': 'XAUUSD',
            'direction': 'BUY',
            'confidence': 0.85,
            'entry_price': 2650.50,
            'stop_loss': 2640.00,
            'take_profit': 2670.00,
            'h4_trend': 'BULLISH',
            'h1_momentum': 'STRONG_UP',
            'm15_pattern': 'HAMMER',
            'model_used': 'transformer',
            'features': '{"rsi": 65, "macd": "bullish", "ema_cross": "golden"}'
        }
        
        # Insert signal into database
        signal_id = await db.insert_signal(test_signal)
        print(f"✅ Test signal created with ID: {signal_id}")
        
        # Test Telegram notification (if configured)
        if config.telegram.enabled and config.telegram.bot_token:
            telegram_bot = TelegramBot(config)
            
            # Format signal message
            message = f"""🚨 *XAUUSD Signal Alert* 🚨

📊 *Direction:* {test_signal['direction']}
📈 *Confidence:* {test_signal['confidence']:.2%}
💰 *Entry:* ${test_signal['entry_price']}
🛑 *Stop Loss:* ${test_signal['stop_loss']}
🎯 *Take Profit:* ${test_signal['take_profit']}

📋 *Analysis:*
• H4 Trend: {test_signal['h4_trend']}
• H1 Momentum: {test_signal['h1_momentum']}
• 15M Pattern: {test_signal['m15_pattern']}

⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 *Model:* {test_signal['model_used']}"""
            
            print("📱 Sending Telegram notification...")
            print(f"Message preview:\n{message}")
            
            # Note: Actual sending would require valid bot token and chat ID
            print("📱 Telegram notification prepared (requires valid bot token)")
        else:
            print("📱 Telegram not configured - skipping notification test")
        
        # Get active signals
        active_signals = await db.get_active_signals()
        print(f"📊 Active signals in database: {len(active_signals)}")
        
        print("✅ Signal system test completed!")
        
    except Exception as e:
        print(f"❌ Error testing signal system: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_signal_system())
