# Google Colab Training Guide for Forex AI System

## Overview
This guide will help you move your AI model training from local PC to Google Colab for faster training with GPU acceleration.

## Prerequisites
- Google account with Google Drive access
- Basic understanding of Jupyter notebooks
- Your forex data exported from local database

## Step 1: Prepare Your Data

### Export Data from Local System
Run this script on your local machine to export training data:

```python
# Run this in your local environment
python export_training_data.py
```

This will create:
- `training_data_15m.csv`
- `training_data_1h.csv` 
- `training_data_4h.csv`
- `model_config.json`

### Upload to Google Drive
1. Create a folder in Google Drive: `ForexAI_Training`
2. Upload the exported CSV files and config
3. Note the folder path for Colab access

## Step 2: Setup Google Colab Environment

### Open the Training Notebook
1. Go to [Google Colab](https://colab.research.google.com/)
2. Upload the `colab_training_notebook.ipynb` file
3. Or create a new notebook and copy the code sections

### Enable GPU Acceleration
1. In Colab: Runtime → Change runtime type
2. Select "GPU" as Hardware accelerator
3. Choose "High-RAM" if available (for large datasets)

## Step 3: Install Dependencies

The notebook will automatically install required packages:
- PyTorch with CUDA support
- Transformers and advanced ML libraries
- Technical analysis libraries
- Visualization tools

## Step 4: Training Process

### Data Loading and Preprocessing
- Mount Google Drive
- Load your exported CSV files
- Apply feature engineering pipeline
- Create train/validation splits

### Model Training Options

#### Option 1: Quick Training (Recommended for testing)
- Reduced epochs (20-50)
- Smaller batch sizes
- Basic hyperparameter settings
- Training time: 30-60 minutes

#### Option 2: Full Training (Production quality)
- Full epochs (100-200)
- Hyperparameter optimization with Optuna
- Cross-validation
- Training time: 2-4 hours

#### Option 3: Advanced Training (Best performance)
- Extended training with learning rate scheduling
- Advanced data augmentation
- Ensemble model training
- Training time: 4-8 hours

## Step 5: Model Export and Download

After training:
1. Models are automatically saved to Google Drive
2. Download trained models (.pth files)
3. Download training logs and metrics
4. Transfer back to your local system

## Step 6: Integration with Local System

### Update Local Configuration
1. Place downloaded models in `models/saved_models/`
2. Update `config.yaml` with new model paths
3. Test model loading and inference

### Verify Model Performance
```python
# Test the trained models locally
python test_trained_models.py
```

## Benefits of Colab Training

### Performance Improvements
- **GPU Acceleration**: 10-50x faster training
- **High RAM**: Handle larger datasets
- **Parallel Processing**: Train multiple models simultaneously

### Cost Benefits
- **Free GPU hours**: 12+ hours daily with free tier
- **Colab Pro**: $10/month for extended usage
- **No local hardware requirements**

### Convenience Features
- **Cloud Storage**: Automatic backup to Google Drive
- **Collaboration**: Share notebooks with team
- **Version Control**: Built-in notebook versioning

## Troubleshooting

### Common Issues

#### Out of Memory Errors
- Reduce batch size in config
- Use gradient accumulation
- Enable mixed precision training

#### Disconnection Issues
- Save checkpoints frequently
- Use Colab Pro for longer sessions
- Implement auto-resume functionality

#### Data Loading Problems
- Verify Google Drive mounting
- Check file paths and permissions
- Ensure CSV files are properly formatted

### Performance Optimization Tips

#### Data Loading
- Use efficient data loaders
- Implement data caching
- Optimize feature engineering pipeline

#### Training Speed
- Use mixed precision (FP16)
- Optimize batch sizes for GPU memory
- Enable gradient checkpointing for large models

#### Memory Management
- Clear unused variables
- Use gradient accumulation
- Implement model checkpointing

## Next Steps

1. **Setup**: Follow Step 1-2 to prepare your environment
2. **Test Run**: Start with Option 1 (Quick Training) to verify setup
3. **Production**: Move to Option 2-3 for final model training
4. **Deploy**: Integrate trained models back to your local system

## Support Files

- `colab_training_notebook.ipynb` - Main training notebook
- `colab_requirements.txt` - Package requirements
- `colab_utils.py` - Helper functions
- `export_training_data.py` - Data export script
- `model_export.py` - Model download utilities

## Estimated Timeline

- **Setup**: 30 minutes
- **Data Preparation**: 15 minutes  
- **Quick Training**: 1 hour
- **Full Training**: 3-4 hours
- **Integration**: 30 minutes

**Total**: 5-6 hours for complete migration and training

Ready to start? Open the `colab_training_notebook.ipynb` file in Google Colab!
