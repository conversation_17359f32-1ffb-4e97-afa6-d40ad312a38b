# 🤖 Real-Time AI-Powered Forex Signal Generation System (XAUUSD)

A comprehensive, self-learning AI system that monitors the XAUUSD (Gold/USD) Forex market in real-time, analyzes multi-timeframes (H4, H1, 15M), and generates high-probability trading signals with advanced machine learning models.

## 🌟 Features

### 🧠 Advanced AI Models
- **Transformer-based Architecture**: Attention mechanism for long-range dependencies
- **CNN-LSTM Hybrid**: Pattern recognition combined with sequence modeling
- **Ensemble Learning**: Multiple models for robust predictions
- **Self-Learning Pipeline**: Automatic model retraining based on performance

### 📊 Multi-Timeframe Analysis
- **H4 (4-Hour)**: Macro trend analysis and market regime detection
- **H1 (1-Hour)**: Momentum detection and pullback confirmation
- **15M (15-Minute)**: Precise entry timing and pattern recognition

### 🎯 Signal Generation
- **Real-time Monitoring**: Continuous market analysis
- **Multi-layer Validation**: Cross-timeframe signal confirmation
- **Risk Management**: Automatic stop-loss and take-profit calculation
- **Confidence Scoring**: AI-powered signal quality assessment

### 📱 Signal Delivery
- **Telegram <PERSON>**: Instant signal notifications with rich formatting
- **Web Dashboard**: Real-time charts and performance analytics
- **REST API**: Integration with external systems
- **Multiple Channels**: Extensible delivery system

### 🔧 Technical Features
- **50+ Technical Indicators**: Comprehensive market analysis
- **Candlestick Pattern Recognition**: Advanced price action analysis
- **Support/Resistance Detection**: Dynamic level identification
- **Volume Analysis**: Market participation insights
- **Volatility Regime Detection**: Market condition awareness

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Docker & Docker Compose (for containerized deployment)
- AWS Account (for cloud deployment)

### 1. Clone Repository
```bash
git clone <repository-url>
cd forex-ai-system
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
nano .env
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Initialize System
```bash
# Initialize database and collect historical data
python main.py --mode init

# Train AI models
python main.py --mode train
```

### 5. Run System
```bash
# Run complete system
python main.py --mode full

# Or run specific components
python main.py --mode signals    # Signal generation only
python main.py --mode dashboard  # Web dashboard only
```

## 🐳 Docker Deployment

### Local Development
```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f forex-ai

# Stop services
docker-compose down
```

### Production Deployment
```bash
# Build production image
docker build -t forex-ai-system .

# Run with production settings
docker run -d \
  --name forex-ai \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  forex-ai-system
```

## ☁️ AWS Deployment

### Automated Deployment
```bash
# Install AWS CLI and configure credentials
aws configure

# Run deployment script
python deployment/aws_deploy.py --region us-east-1 --instance-type t3.medium

# Follow the output instructions for SSH access
```

### Manual AWS Setup
1. Launch EC2 instance (t3.medium or larger)
2. Install Docker and Docker Compose
3. Clone repository and configure environment
4. Run with Docker Compose

## 📊 Configuration

### Core Settings (`config.yaml`)
```yaml
trading:
  symbol: "XAUUSD"
  timeframes: ["15m", "1h", "4h"]
  signals:
    min_confidence: 0.75
    max_signals_per_day: 10
    risk_reward_ratio: 3.0

ai_models:
  primary_model: "transformer"
  ensemble_models: ["transformer", "cnn_lstm"]
```

### Environment Variables (`.env`)
```bash
# API Keys
ALPHA_VANTAGE_API_KEY=your_key_here
TWELVEDATA_API_KEY=your_key_here

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# AWS (for deployment)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
```

## 🔧 API Endpoints

### Health & Status
- `GET /api/health` - System health check
- `GET /api/system/status` - Detailed system status

### Signals
- `GET /api/signals/recent` - Recent signals
- `GET /api/signals/active` - Active signals
- `POST /api/signals/generate` - Manual signal generation

### Performance
- `GET /api/performance/stats` - Trading statistics
- `GET /api/performance/detailed` - Detailed performance metrics

### Data
- `GET /api/data/market` - Market data
- `GET /api/data/export` - Export historical data

## 🤖 Telegram Bot Commands

- `/start` - Welcome message and setup
- `/help` - Available commands
- `/status` - System status
- `/stats` - Trading statistics
- `/signals` - Recent signals
- `/performance` - Model performance

## 📈 Performance Monitoring

### Key Metrics
- **Win Rate**: Percentage of profitable signals
- **Profit Factor**: Ratio of gross profit to gross loss
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Average P&L per Trade**: Expected value per signal

### Model Evaluation
- **Accuracy**: Prediction correctness
- **Precision/Recall**: Signal quality metrics
- **F1 Score**: Balanced performance measure
- **Confidence Calibration**: Reliability of confidence scores

## 🔄 Self-Learning Pipeline

1. **Signal Generation**: AI models generate trading signals
2. **Trade Execution**: Signals are tracked for outcomes
3. **Performance Analysis**: Results are analyzed and stored
4. **Model Retraining**: Models are retrained with new data
5. **Model Selection**: Best performing models are deployed

## 🛡️ Risk Management

### Built-in Safety Features
- **Position Sizing**: Automatic risk calculation
- **Stop Loss**: Mandatory stop-loss levels
- **Daily Limits**: Maximum signals per day
- **Confidence Thresholds**: Minimum confidence requirements
- **Multi-timeframe Validation**: Cross-timeframe confirmation

### Risk Warnings
- **Educational Purpose**: Signals are for educational use
- **No Guarantee**: Past performance doesn't guarantee future results
- **Risk Capital**: Only trade with money you can afford to lose
- **Professional Advice**: Consult financial advisors for investment decisions

## 🔧 Development

### Project Structure
```
forex-ai-system/
├── src/
│   ├── data/           # Data collection and processing
│   ├── models/         # AI models and training
│   ├── signals/        # Signal generation and delivery
│   ├── api/           # Web API and dashboard
│   └── utils/         # Utilities and configuration
├── config.yaml        # System configuration
├── requirements.txt   # Python dependencies
├── Dockerfile        # Container configuration
└── deployment/       # Deployment scripts
```

### Adding New Models
1. Create model class in `src/models/ai_models/`
2. Add training logic in `src/models/training/`
3. Update configuration in `config.yaml`
4. Register model in ensemble

### Adding New Indicators
1. Add indicator calculation in `src/data/processors/feature_engineer.py`
2. Update feature list in `config.yaml`
3. Retrain models with new features

## 📞 Support

### Getting Help
- Check the logs in `logs/` directory
- Review configuration in `config.yaml`
- Verify environment variables in `.env`
- Check API endpoints for system status

### Common Issues
- **No Data**: Verify API keys and internet connection
- **Model Errors**: Ensure sufficient historical data
- **Telegram Issues**: Check bot token and chat ID
- **Performance Issues**: Consider upgrading instance type

## 📄 License

This project is for educational purposes. Please ensure compliance with your local regulations regarding automated trading systems.

## 🙏 Acknowledgments

- **Technical Analysis Library**: TA-Lib for indicators
- **Machine Learning**: PyTorch and TensorFlow
- **Data Sources**: yfinance, Alpha Vantage, TwelveData
- **Deployment**: AWS, Docker, FastAPI

---

**⚠️ Disclaimer**: This system is for educational and research purposes only. Trading involves substantial risk of loss. Past performance is not indicative of future results. Always conduct your own research and consider seeking advice from a licensed financial advisor.
