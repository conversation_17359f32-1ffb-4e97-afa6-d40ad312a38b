# Core Dependencies (Essential)
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Data Collection & APIs
yfinance>=0.2.18
requests>=2.31.0

# Basic ML packages
xgboost>=1.7.0
lightgbm>=4.0.0

# Technical Analysis
ta>=0.10.2

# Database & Storage
sqlalchemy>=2.0.0

# Web Framework & API
fastapi>=0.100.0
uvicorn>=0.22.0
jinja2>=3.1.0

# Telegram Bot
python-telegram-bot>=20.3
aiohttp>=3.8.0

# Scheduling & Async
apscheduler>=3.10.0

# Visualization & Plotting
plotly>=5.15.0
matplotlib>=3.7.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0
loguru>=0.7.0
tqdm>=4.65.0
joblib>=1.3.0

# Optional Advanced ML (install separately if needed)
# torch>=2.0.0
# tensorflow>=2.13.0
# transformers>=4.30.0
