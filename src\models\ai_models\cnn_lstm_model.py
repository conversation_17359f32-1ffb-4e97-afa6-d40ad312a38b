"""
CNN-LSTM hybrid model for forex signal prediction
Combines CNN for pattern recognition with LSTM for sequence modeling
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Tuple, Dict, Any, Optional
from loguru import logger

from ...utils.config import Config


class CNNLSTMModel(nn.Module):
    """CNN-LSTM hybrid model for forex prediction"""
    
    def __init__(self, config: Config, input_features: int = 50):
        super().__init__()
        
        self.config = config
        cnn_lstm_config = config.ai_models.cnn_lstm
        
        self.input_features = input_features
        self.cnn_filters = cnn_lstm_config.cnn_filters
        self.lstm_units = cnn_lstm_config.lstm_units
        self.dropout = cnn_lstm_config.dropout
        self.sequence_length = config.ai_models.transformer.sequence_length  # Use same sequence length
        
        # CNN layers for pattern recognition
        self.conv_layers = nn.ModuleList()
        in_channels = 1
        
        for filters in self.cnn_filters:
            self.conv_layers.append(
                nn.Sequential(
                    nn.Conv1d(in_channels, filters, kernel_size=3, padding=1),
                    nn.BatchNorm1d(filters),
                    nn.ReLU(),
                    nn.Dropout(self.dropout)
                )
            )
            in_channels = filters
        
        # Calculate CNN output size
        self.cnn_output_size = self.cnn_filters[-1]
        
        # LSTM layers for sequence modeling
        self.lstm = nn.LSTM(
            input_size=self.cnn_output_size,
            hidden_size=self.lstm_units,
            num_layers=2,
            batch_first=True,
            dropout=self.dropout,
            bidirectional=True
        )
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=self.lstm_units * 2,  # Bidirectional LSTM
            num_heads=8,
            dropout=self.dropout,
            batch_first=True
        )
        
        # Output layers
        self.classifier = nn.Sequential(
            nn.Linear(self.lstm_units * 2, self.lstm_units),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.lstm_units, self.lstm_units // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.lstm_units // 2, 3)  # 3 classes: BUY, SELL, HOLD
        )
        
        # Confidence head
        self.confidence_head = nn.Sequential(
            nn.Linear(self.lstm_units * 2, self.lstm_units // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.lstm_units // 2, 1),
            nn.Sigmoid()
        )
        
        # Feature importance layer (for interpretability)
        self.feature_importance = nn.Linear(self.lstm_units * 2, input_features)
        
        logger.info(f"CNN-LSTM model initialized with {self.count_parameters()} parameters")
    
    def count_parameters(self) -> int:
        """Count total number of trainable parameters"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, sequence_length, features)
            
        Returns:
            Tuple of (predictions, confidence, feature_importance)
        """
        batch_size, seq_len, features = x.shape
        
        # Reshape for CNN: (batch_size * seq_len, 1, features)
        x_cnn = x.view(batch_size * seq_len, 1, features)
        
        # Apply CNN layers
        for conv_layer in self.conv_layers:
            x_cnn = conv_layer(x_cnn)
        
        # Global average pooling
        x_cnn = F.adaptive_avg_pool1d(x_cnn, 1).squeeze(-1)
        
        # Reshape back for LSTM: (batch_size, seq_len, cnn_output_size)
        x_lstm = x_cnn.view(batch_size, seq_len, self.cnn_output_size)
        
        # Apply LSTM
        lstm_out, (hidden, cell) = self.lstm(x_lstm)
        
        # Apply attention
        attended_out, attention_weights = self.attention(lstm_out, lstm_out, lstm_out)
        
        # Use the last time step output
        final_output = attended_out[:, -1, :]  # (batch_size, lstm_units * 2)
        
        # Generate predictions and confidence
        predictions = self.classifier(final_output)
        confidence = self.confidence_head(final_output).squeeze(-1)
        
        # Calculate feature importance
        feature_importance = torch.abs(self.feature_importance(final_output))
        
        return predictions, confidence, feature_importance
    
    def predict(self, x: torch.Tensor) -> Dict[str, Any]:
        """
        Make prediction with post-processing
        
        Args:
            x: Input tensor
            
        Returns:
            Dictionary with prediction results
        """
        self.eval()
        with torch.no_grad():
            predictions, confidence, feature_importance = self.forward(x)
            
            # Apply softmax to get probabilities
            probabilities = F.softmax(predictions, dim=-1)
            
            # Get predicted class
            predicted_class = torch.argmax(probabilities, dim=-1)
            
            # Convert to signal format
            class_to_signal = {0: 'HOLD', 1: 'BUY', 2: 'SELL'}
            signals = [class_to_signal[cls.item()] for cls in predicted_class]
            
            return {
                'signals': signals,
                'probabilities': probabilities.cpu().numpy(),
                'confidence': confidence.cpu().numpy(),
                'feature_importance': feature_importance.cpu().numpy(),
                'raw_predictions': predictions.cpu().numpy()
            }


class PricePatternCNN(nn.Module):
    """Specialized CNN for price pattern recognition"""
    
    def __init__(self, config: Config):
        super().__init__()
        
        self.config = config
        
        # Multi-scale CNN for different pattern sizes
        self.conv_blocks = nn.ModuleList([
            # Short-term patterns (3-5 candles)
            nn.Sequential(
                nn.Conv1d(5, 32, kernel_size=3, padding=1),  # OHLCV
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Conv1d(32, 64, kernel_size=3, padding=1),
                nn.BatchNorm1d(64),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ),
            # Medium-term patterns (5-10 candles)
            nn.Sequential(
                nn.Conv1d(5, 32, kernel_size=5, padding=2),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Conv1d(32, 64, kernel_size=5, padding=2),
                nn.BatchNorm1d(64),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ),
            # Long-term patterns (10-20 candles)
            nn.Sequential(
                nn.Conv1d(5, 32, kernel_size=7, padding=3),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Conv1d(32, 64, kernel_size=7, padding=3),
                nn.BatchNorm1d(64),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            )
        ])
        
        # Fusion layer
        self.fusion = nn.Sequential(
            nn.Linear(64 * 3, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64)
        )
        
    def forward(self, ohlcv_data: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for pattern recognition
        
        Args:
            ohlcv_data: OHLCV data tensor (batch_size, sequence_length, 5)
            
        Returns:
            Pattern features tensor
        """
        batch_size, seq_len, features = ohlcv_data.shape
        
        # Transpose for conv1d: (batch_size, features, seq_len)
        x = ohlcv_data.transpose(1, 2)
        
        # Apply multi-scale convolutions
        pattern_features = []
        for conv_block in self.conv_blocks:
            features = conv_block(x).squeeze(-1)  # Remove last dimension
            pattern_features.append(features)
        
        # Concatenate features from different scales
        combined_features = torch.cat(pattern_features, dim=1)
        
        # Apply fusion layer
        fused_features = self.fusion(combined_features)
        
        return fused_features


class EnhancedCNNLSTM(nn.Module):
    """Enhanced CNN-LSTM with price pattern recognition"""
    
    def __init__(self, config: Config, input_features: int = 50):
        super().__init__()
        
        self.config = config
        self.input_features = input_features
        
        # Price pattern CNN
        self.pattern_cnn = PricePatternCNN(config)
        
        # Feature CNN-LSTM (for technical indicators)
        self.feature_cnn_lstm = CNNLSTMModel(config, input_features)
        
        # Fusion layer
        self.fusion = nn.Sequential(
            nn.Linear(64 + config.ai_models.cnn_lstm.lstm_units * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Final classifier
        self.final_classifier = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 3)
        )
        
        # Confidence head
        self.confidence_head = nn.Sequential(
            nn.Linear(128, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
    
    def forward(self, features: torch.Tensor, ohlcv: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass with both features and OHLCV data
        
        Args:
            features: Technical indicator features
            ohlcv: OHLCV price data
            
        Returns:
            Tuple of (predictions, confidence)
        """
        # Extract pattern features
        pattern_features = self.pattern_cnn(ohlcv)
        
        # Extract feature-based predictions (ignore confidence and importance for fusion)
        feature_predictions, _, _ = self.feature_cnn_lstm(features)
        
        # Get the intermediate representation from feature CNN-LSTM
        # We need to modify this to get intermediate features instead of final predictions
        # For now, we'll use a simplified approach
        
        # Combine pattern and feature representations
        # This is a simplified fusion - in practice, you'd want to extract intermediate features
        combined_features = torch.cat([pattern_features, feature_predictions], dim=1)
        
        # Apply fusion
        fused = self.fusion(combined_features)
        
        # Final predictions
        predictions = self.final_classifier(fused)
        confidence = self.confidence_head(fused).squeeze(-1)
        
        return predictions, confidence
    
    def predict(self, features: torch.Tensor, ohlcv: torch.Tensor) -> Dict[str, Any]:
        """Make prediction with both feature and pattern data"""
        self.eval()
        with torch.no_grad():
            predictions, confidence = self.forward(features, ohlcv)
            
            # Apply softmax to get probabilities
            probabilities = F.softmax(predictions, dim=-1)
            
            # Get predicted class
            predicted_class = torch.argmax(probabilities, dim=-1)
            
            # Convert to signal format
            class_to_signal = {0: 'HOLD', 1: 'BUY', 2: 'SELL'}
            signals = [class_to_signal[cls.item()] for cls in predicted_class]
            
            return {
                'signals': signals,
                'probabilities': probabilities.cpu().numpy(),
                'confidence': confidence.cpu().numpy(),
                'raw_predictions': predictions.cpu().numpy()
            }
