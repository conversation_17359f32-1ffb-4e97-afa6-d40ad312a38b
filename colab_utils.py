"""
Utility functions for Google Colab training environment
"""

import os
import json
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Tuple, Optional, List
import matplotlib.pyplot as plt
import seaborn as sns
from google.colab import drive, files
import zipfile
import shutil


class ColabEnvironment:
    """Manage Google Colab environment setup and utilities"""
    
    def __init__(self):
        self.drive_mounted = False
        self.data_path = None
        self.models_path = None
        
    def setup_environment(self):
        """Setup the complete Colab environment"""
        print("🚀 Setting up Google Colab environment for Forex AI training...")
        
        # Mount Google Drive
        self.mount_drive()
        
        # Setup directories
        self.setup_directories()
        
        # Check GPU availability
        self.check_gpu()
        
        print("✅ Environment setup completed!")
    
    def mount_drive(self):
        """Mount Google Drive"""
        try:
            drive.mount('/content/drive')
            self.drive_mounted = True
            print("✅ Google Drive mounted successfully")
        except Exception as e:
            print(f"❌ Failed to mount Google Drive: {e}")
            raise
    
    def setup_directories(self):
        """Setup working directories"""
        # Set paths
        self.data_path = Path('/content/drive/MyDrive/ForexAI_Training')
        self.models_path = Path('/content/models')
        self.output_path = Path('/content/output')
        
        # Create local directories
        self.models_path.mkdir(exist_ok=True)
        self.output_path.mkdir(exist_ok=True)
        
        # Verify data directory exists
        if not self.data_path.exists():
            print(f"❌ Data directory not found: {self.data_path}")
            print("Please upload your exported data to Google Drive in 'ForexAI_Training' folder")
            raise FileNotFoundError("Training data not found in Google Drive")
        
        print(f"✅ Data directory found: {self.data_path}")
        print(f"✅ Models directory: {self.models_path}")
        print(f"✅ Output directory: {self.output_path}")
    
    def check_gpu(self):
        """Check GPU availability and setup"""
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"✅ GPU available: {gpu_name}")
            print(f"✅ GPU memory: {gpu_memory:.1f} GB")
            
            # Set optimal settings for GPU
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
        else:
            print("⚠️  No GPU available - training will be slower")
            print("Go to Runtime → Change runtime type → Select GPU")
    
    def load_training_data(self) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Load exported training data and configuration"""
        try:
            # Load main training data
            training_file = self.data_path / "training_data_15m.csv"
            if not training_file.exists():
                raise FileNotFoundError(f"Training data not found: {training_file}")
            
            print(f"📊 Loading training data from {training_file}")
            data = pd.read_csv(training_file)
            print(f"✅ Loaded training data: {data.shape}")
            
            # Load configuration
            config_file = self.data_path / "model_config.json"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print("✅ Loaded model configuration")
            else:
                print("⚠️  Model config not found, using defaults")
                config = self._get_default_config()
            
            # Load data summary if available
            summary_file = self.data_path / "data_summary.json"
            if summary_file.exists():
                with open(summary_file, 'r') as f:
                    summary = json.load(f)
                print("✅ Loaded data summary")
                self._print_data_summary(summary)
            
            return data, config
            
        except Exception as e:
            print(f"❌ Error loading training data: {e}")
            raise
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if config file not found"""
        return {
            'ai_models': {
                'primary_model': 'transformer',
                'ensemble_models': ['transformer', 'cnn_lstm'],
                'transformer': {
                    'sequence_length': 100,
                    'd_model': 256,
                    'nhead': 8,
                    'num_layers': 6,
                    'dropout': 0.1
                },
                'cnn_lstm': {
                    'cnn_filters': [32, 64, 128],
                    'lstm_units': 128,
                    'dropout': 0.2
                },
                'training': {
                    'batch_size': 32,
                    'epochs': 50,  # Reduced for Colab
                    'learning_rate': 0.001,
                    'validation_split': 0.2,
                    'early_stopping_patience': 10
                }
            }
        }
    
    def _print_data_summary(self, summary: Dict[str, Any]):
        """Print data summary information"""
        print("\n📈 Data Summary:")
        print(f"  Shape: {summary.get('data_shape', 'Unknown')}")
        print(f"  Features: {len(summary.get('feature_columns', []))}")
        print(f"  Date range: {summary.get('date_range', {}).get('start', 'Unknown')} to {summary.get('date_range', {}).get('end', 'Unknown')}")
        
        # Check for missing values
        missing = summary.get('missing_values', {})
        missing_count = sum(v for v in missing.values() if v > 0)
        if missing_count > 0:
            print(f"  ⚠️  Missing values: {missing_count} total")
        else:
            print("  ✅ No missing values")
    
    def save_model(self, model: torch.nn.Module, model_name: str, 
                   metrics: Dict[str, float], config: Dict[str, Any]):
        """Save trained model with metadata"""
        try:
            # Create model save directory
            save_dir = self.output_path / f"{model_name}_trained"
            save_dir.mkdir(exist_ok=True)
            
            # Save model state dict
            model_file = save_dir / f"{model_name}.pth"
            torch.save(model.state_dict(), model_file)
            
            # Save model metadata
            metadata = {
                'model_name': model_name,
                'model_type': type(model).__name__,
                'metrics': metrics,
                'config': config,
                'parameters': sum(p.numel() for p in model.parameters()),
                'trainable_parameters': sum(p.numel() for p in model.parameters() if p.requires_grad)
            }
            
            metadata_file = save_dir / f"{model_name}_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✅ Model saved: {save_dir}")
            
            # Also save to Google Drive
            drive_save_dir = self.data_path / "trained_models" / f"{model_name}_trained"
            drive_save_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy files to Drive
            shutil.copy2(model_file, drive_save_dir)
            shutil.copy2(metadata_file, drive_save_dir)
            
            print(f"✅ Model backed up to Google Drive: {drive_save_dir}")
            
        except Exception as e:
            print(f"❌ Error saving model: {e}")
            raise
    
    def download_models(self):
        """Download all trained models as a zip file"""
        try:
            # Create zip file with all models
            zip_path = "/content/trained_models.zip"
            
            with zipfile.ZipFile(zip_path, 'w') as zipf:
                for model_dir in self.output_path.glob("*_trained"):
                    for file_path in model_dir.rglob("*"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(self.output_path)
                            zipf.write(file_path, arcname)
            
            # Download the zip file
            files.download(zip_path)
            print("✅ Models downloaded successfully!")
            
        except Exception as e:
            print(f"❌ Error downloading models: {e}")
    
    def plot_training_history(self, history: List[Dict[str, float]], model_name: str):
        """Plot training history"""
        try:
            # Convert history to DataFrame
            df = pd.DataFrame(history)
            
            # Create subplots
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Training History - {model_name}', fontsize=16)
            
            # Plot loss
            axes[0, 0].plot(df['epoch'], df['train_loss'], label='Train Loss', color='blue')
            axes[0, 0].plot(df['epoch'], df['val_loss'], label='Validation Loss', color='red')
            axes[0, 0].set_title('Loss')
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].legend()
            axes[0, 0].grid(True)
            
            # Plot accuracy
            if 'val_accuracy' in df.columns:
                axes[0, 1].plot(df['epoch'], df['val_accuracy'], label='Validation Accuracy', color='green')
                axes[0, 1].set_title('Accuracy')
                axes[0, 1].set_xlabel('Epoch')
                axes[0, 1].set_ylabel('Accuracy')
                axes[0, 1].legend()
                axes[0, 1].grid(True)
            
            # Plot F1 score
            if 'val_f1' in df.columns:
                axes[1, 0].plot(df['epoch'], df['val_f1'], label='Validation F1', color='orange')
                axes[1, 0].set_title('F1 Score')
                axes[1, 0].set_xlabel('Epoch')
                axes[1, 0].set_ylabel('F1 Score')
                axes[1, 0].legend()
                axes[1, 0].grid(True)
            
            # Plot learning rate if available
            if 'learning_rate' in df.columns:
                axes[1, 1].plot(df['epoch'], df['learning_rate'], label='Learning Rate', color='purple')
                axes[1, 1].set_title('Learning Rate')
                axes[1, 1].set_xlabel('Epoch')
                axes[1, 1].set_ylabel('Learning Rate')
                axes[1, 1].legend()
                axes[1, 1].grid(True)
                axes[1, 1].set_yscale('log')
            
            plt.tight_layout()
            
            # Save plot
            plot_file = self.output_path / f"{model_name}_training_history.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.show()
            
            print(f"✅ Training history plot saved: {plot_file}")
            
        except Exception as e:
            print(f"❌ Error plotting training history: {e}")


def install_requirements():
    """Install required packages for Colab training"""
    requirements = [
        "torch>=2.0.0",
        "transformers>=4.30.0",
        "optuna>=3.2.0",
        "ta>=0.10.2",
        "loguru>=0.7.0"
    ]
    
    print("📦 Installing required packages...")
    for req in requirements:
        os.system(f"pip install {req}")
    
    print("✅ All packages installed!")


def get_device():
    """Get the best available device (GPU/CPU)"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"🚀 Using GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device('cpu')
        print("💻 Using CPU")
    
    return device
