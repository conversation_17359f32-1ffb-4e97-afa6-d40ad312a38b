"""
Model training pipeline for Forex AI System
Supports multiple model types with hyperparameter optimization
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import optuna
from pathlib import Path
from typing import Dict, Any, Tuple, Optional
from loguru import logger
import joblib

from ...utils.config import Config
from ...utils.database import DatabaseManager
from ...data.collectors.data_collector import DataCollector
from ...data.processors.feature_engineer import FeatureEngineer
from ..ai_models.transformer_model import ForexTransformer, TransformerDataProcessor
from ..ai_models.cnn_lstm_model import CNNLSTMModel


class ModelTrainer:
    """Model training and evaluation pipeline"""
    
    def __init__(self, config: Config):
        """Initialize model trainer"""
        self.config = config
        self.db_manager = DatabaseManager(config)
        self.data_collector = DataCollector(config)
        self.feature_engineer = FeatureEngineer(config)
        
        # Create models directory
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Device configuration
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")
        
        # Training configuration
        self.training_config = config.ai_models.training
        
        logger.info("Model trainer initialized")
    
    async def prepare_training_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Prepare training data from all timeframes"""
        logger.info("Preparing training data...")
        
        try:
            # Get data for all timeframes
            data_15m = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "15m"
            )
            data_1h = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "1h"
            )
            data_4h = await self.db_manager.get_historical_data(
                self.config.trading.symbol, "4h"
            )
            
            if data_15m.empty:
                raise ValueError("No 15m data available for training")
            
            logger.info(f"Loaded data - 15m: {len(data_15m)}, 1h: {len(data_1h)}, 4h: {len(data_4h)}")
            
            # Engineer features
            featured_data = self.feature_engineer.engineer_features(
                data_15m, data_1h if not data_1h.empty else None, data_4h if not data_4h.empty else None
            )
            
            logger.info(f"Feature engineering completed. Shape: {featured_data.shape}")
            
            return featured_data, data_1h, data_4h
            
        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            raise
    
    def create_model(self, model_type: str, input_features: int) -> nn.Module:
        """Create model instance based on type"""
        if model_type == "transformer":
            return ForexTransformer(self.config)
        elif model_type == "cnn_lstm":
            return CNNLSTMModel(self.config, input_features)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def train_epoch(self, model: nn.Module, dataloader: DataLoader, optimizer: optim.Optimizer, 
                   criterion: nn.Module, device: torch.device) -> float:
        """Train model for one epoch"""
        model.train()
        total_loss = 0.0
        
        for batch_idx, (data, targets) in enumerate(dataloader):
            data, targets = data.to(device), targets.to(device)
            
            optimizer.zero_grad()
            
            if isinstance(model, ForexTransformer):
                predictions, confidence = model(data)
                # Combine classification loss and confidence loss
                class_loss = criterion(predictions, targets)
                # Confidence should be higher for correct predictions
                correct_mask = (torch.argmax(predictions, dim=1) == targets).float()
                confidence_loss = nn.MSELoss()(confidence, correct_mask)
                loss = class_loss + 0.1 * confidence_loss
            else:
                predictions, confidence, _ = model(data)
                class_loss = criterion(predictions, targets)
                correct_mask = (torch.argmax(predictions, dim=1) == targets).float()
                confidence_loss = nn.MSELoss()(confidence, correct_mask)
                loss = class_loss + 0.1 * confidence_loss
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        return total_loss / len(dataloader)
    
    def evaluate_model(self, model: nn.Module, dataloader: DataLoader, 
                      criterion: nn.Module, device: torch.device) -> Dict[str, float]:
        """Evaluate model performance"""
        model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        all_confidences = []
        
        with torch.no_grad():
            for data, targets in dataloader:
                data, targets = data.to(device), targets.to(device)
                
                if isinstance(model, ForexTransformer):
                    predictions, confidence = model(data)
                else:
                    predictions, confidence, _ = model(data)
                
                loss = criterion(predictions, targets)
                total_loss += loss.item()
                
                # Store predictions for metrics calculation
                pred_classes = torch.argmax(predictions, dim=1)
                all_predictions.extend(pred_classes.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_confidences.extend(confidence.cpu().numpy())
        
        # Calculate metrics
        accuracy = accuracy_score(all_targets, all_predictions)
        precision = precision_score(all_targets, all_predictions, average='weighted', zero_division=0)
        recall = recall_score(all_targets, all_predictions, average='weighted', zero_division=0)
        f1 = f1_score(all_targets, all_predictions, average='weighted', zero_division=0)
        avg_confidence = np.mean(all_confidences)
        
        return {
            'loss': total_loss / len(dataloader),
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'avg_confidence': avg_confidence
        }
    
    async def train_model(self, model_type: str, optimize_hyperparameters: bool = True) -> Dict[str, Any]:
        """Train a specific model type"""
        logger.info(f"Starting training for {model_type} model")
        
        try:
            # Prepare data
            featured_data, _, _ = await self.prepare_training_data()
            
            # Prepare data processor
            if model_type == "transformer":
                data_processor = TransformerDataProcessor(self.config)
                X, y = data_processor.prepare_training_data(featured_data)
            else:
                # For CNN-LSTM, use similar data preparation
                data_processor = TransformerDataProcessor(self.config)
                X, y = data_processor.prepare_training_data(featured_data)
            
            logger.info(f"Training data prepared: X shape {X.shape}, y shape {y.shape}")
            
            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=self.training_config.validation_split, 
                random_state=42, stratify=y
            )
            
            # Create data loaders
            train_dataset = TensorDataset(X_train, y_train)
            val_dataset = TensorDataset(X_val, y_val)
            
            train_loader = DataLoader(
                train_dataset, 
                batch_size=self.training_config.batch_size, 
                shuffle=True
            )
            val_loader = DataLoader(
                val_dataset, 
                batch_size=self.training_config.batch_size, 
                shuffle=False
            )
            
            # Hyperparameter optimization
            if optimize_hyperparameters:
                best_params = await self.optimize_hyperparameters(
                    model_type, train_loader, val_loader, X.shape[-1]
                )
            else:
                best_params = {}
            
            # Train final model with best parameters
            model = self.create_model(model_type, X.shape[-1])
            model.to(self.device)
            
            # Apply best parameters if available
            if best_params:
                # Update model configuration with best parameters
                # This would require modifying the model creation to accept parameters
                pass
            
            # Setup training
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(
                model.parameters(), 
                lr=best_params.get('learning_rate', self.training_config.learning_rate)
            )
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode='min', patience=5, factor=0.5
            )
            
            # Training loop
            best_val_loss = float('inf')
            patience_counter = 0
            training_history = []
            
            for epoch in range(self.training_config.epochs):
                # Train
                train_loss = self.train_epoch(model, train_loader, optimizer, criterion, self.device)
                
                # Validate
                val_metrics = self.evaluate_model(model, val_loader, criterion, self.device)
                val_loss = val_metrics['loss']
                
                # Learning rate scheduling
                scheduler.step(val_loss)
                
                # Log progress
                logger.info(
                    f"Epoch {epoch+1}/{self.training_config.epochs} - "
                    f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, "
                    f"Val Acc: {val_metrics['accuracy']:.4f}"
                )
                
                # Save training history
                training_history.append({
                    'epoch': epoch + 1,
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                    'val_accuracy': val_metrics['accuracy'],
                    'val_f1': val_metrics['f1_score']
                })
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    
                    # Save best model
                    model_path = self.models_dir / f"{model_type}_best.pth"
                    torch.save({
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'epoch': epoch,
                        'val_loss': val_loss,
                        'val_metrics': val_metrics,
                        'config': self.config,
                        'feature_columns': data_processor.feature_columns,
                        'scaler': data_processor.scaler
                    }, model_path)
                    
                    logger.info(f"Best model saved to {model_path}")
                    
                else:
                    patience_counter += 1
                    if patience_counter >= self.training_config.early_stopping_patience:
                        logger.info(f"Early stopping triggered after {epoch+1} epochs")
                        break
            
            # Final evaluation
            final_metrics = self.evaluate_model(model, val_loader, criterion, self.device)
            
            # Save training history
            history_path = self.models_dir / f"{model_type}_training_history.joblib"
            joblib.dump(training_history, history_path)
            
            # Update model performance in database
            await self.db_manager.update_model_performance({
                'model_name': model_type,
                'version': '1.0',
                'accuracy': final_metrics['accuracy'],
                'precision': final_metrics['precision'],
                'recall': final_metrics['recall'],
                'f1_score': final_metrics['f1_score'],
                'total_trades': len(y_val)
            })
            
            logger.info(f"Training completed for {model_type}")
            
            return {
                'model_type': model_type,
                'final_metrics': final_metrics,
                'best_params': best_params,
                'training_history': training_history,
                'model_path': str(model_path)
            }
            
        except Exception as e:
            logger.error(f"Error training {model_type} model: {e}")
            raise
    
    async def optimize_hyperparameters(self, model_type: str, train_loader: DataLoader, 
                                     val_loader: DataLoader, input_features: int) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna"""
        logger.info(f"Starting hyperparameter optimization for {model_type}")
        
        def objective(trial):
            # Suggest hyperparameters
            learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True)
            batch_size = trial.suggest_categorical('batch_size', [16, 32, 64])
            dropout = trial.suggest_float('dropout', 0.1, 0.5)
            
            # Create model with suggested parameters
            # Note: This would require modifying model creation to accept parameters
            model = self.create_model(model_type, input_features)
            model.to(self.device)
            
            # Setup training
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            
            # Train for a few epochs
            best_val_loss = float('inf')
            for epoch in range(10):  # Limited epochs for optimization
                train_loss = self.train_epoch(model, train_loader, optimizer, criterion, self.device)
                val_metrics = self.evaluate_model(model, val_loader, criterion, self.device)
                
                if val_metrics['loss'] < best_val_loss:
                    best_val_loss = val_metrics['loss']
            
            return best_val_loss
        
        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=20)  # Limited trials for demo
        
        logger.info(f"Best hyperparameters: {study.best_params}")
        
        return study.best_params
    
    def load_model(self, model_type: str, model_path: Optional[str] = None) -> Tuple[nn.Module, Dict[str, Any]]:
        """Load trained model"""
        if model_path is None:
            model_path = self.models_dir / f"{model_type}_best.pth"
        
        if not Path(model_path).exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # Create model (we need to infer input features from saved data)
        # This is a simplified approach - in practice, save model architecture info
        model = self.create_model(model_type, 50)  # Default feature count
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        model.eval()
        
        logger.info(f"Model loaded from {model_path}")
        
        return model, checkpoint
