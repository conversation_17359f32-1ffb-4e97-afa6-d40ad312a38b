"""
Real-time signal generation engine for Forex AI System
Combines multiple AI models with validation layers
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
import torch
from loguru import logger

from ...utils.config import Config
from ...utils.database import DatabaseManager
from ...data.collectors.data_collector import DataCollector
from ...data.processors.feature_engineer import FeatureEngineer
from ...models.training.trainer import ModelTrainer
from ...models.ai_models.transformer_model import TransformerDataProcessor
from ..validators.multi_timeframe_validator import MultiTimeframeValidator


class SignalGenerator:
    """Real-time signal generation engine"""
    
    def __init__(self, config: Config):
        """Initialize signal generator"""
        self.config = config
        self.db_manager = DatabaseManager(config)
        self.data_collector = DataCollector(config)
        self.feature_engineer = FeatureEngineer(config)
        self.model_trainer = ModelTrainer(config)
        self.validator = MultiTimeframeValidator(config)
        
        # Loaded models
        self.models = {}
        self.data_processors = {}
        
        # Signal tracking
        self.last_signal_time = {}
        self.daily_signal_count = 0
        self.last_reset_date = datetime.utcnow().date()
        
        # Device configuration
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        logger.info("Signal generator initialized")
    
    async def load_models(self):
        """Load all trained models"""
        logger.info("Loading trained models...")
        
        try:
            for model_name in self.config.ai_models.ensemble_models:
                try:
                    model, checkpoint = self.model_trainer.load_model(model_name)
                    self.models[model_name] = model
                    
                    # Create data processor
                    if model_name == "transformer":
                        processor = TransformerDataProcessor(self.config)
                        # Restore scaler and feature columns from checkpoint
                        if 'scaler' in checkpoint:
                            processor.scaler = checkpoint['scaler']
                        if 'feature_columns' in checkpoint:
                            processor.feature_columns = checkpoint['feature_columns']
                        self.data_processors[model_name] = processor
                    
                    logger.info(f"Loaded {model_name} model successfully")
                    
                except Exception as e:
                    logger.warning(f"Failed to load {model_name} model: {e}")
            
            if not self.models:
                logger.warning("No models loaded. Training new models...")
                await self.train_initial_models()
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise
    
    async def train_initial_models(self):
        """Train initial models if none are available"""
        logger.info("Training initial models...")
        
        try:
            # Train transformer model
            await self.model_trainer.train_model("transformer", optimize_hyperparameters=False)
            
            # Load the trained model
            await self.load_models()
            
        except Exception as e:
            logger.error(f"Error training initial models: {e}")
            raise
    
    async def get_market_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Get latest market data for all timeframes"""
        try:
            # Get data for all timeframes
            data_15m = await self.data_collector.get_latest_data(
                self.config.trading.symbol, "15m", limit=300
            )
            data_1h = await self.data_collector.get_latest_data(
                self.config.trading.symbol, "1h", limit=100
            )
            data_4h = await self.data_collector.get_latest_data(
                self.config.trading.symbol, "4h", limit=50
            )
            
            if data_15m.empty:
                raise ValueError("No 15m data available")
            
            return data_15m, data_1h, data_4h
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            raise
    
    async def generate_ensemble_prediction(self, featured_data: pd.DataFrame) -> Dict[str, Any]:
        """Generate ensemble prediction from multiple models"""
        try:
            predictions = {}
            confidences = {}
            
            for model_name, model in self.models.items():
                try:
                    # Prepare data for this model
                    processor = self.data_processors.get(model_name)
                    if processor:
                        input_tensor = processor.prepare_prediction_data(featured_data)
                        input_tensor = input_tensor.to(self.device)
                        
                        # Get prediction
                        result = model.predict(input_tensor)
                        
                        predictions[model_name] = result['signals'][0] if result['signals'] else 'HOLD'
                        confidences[model_name] = float(result['confidence'][0]) if len(result['confidence']) > 0 else 0.5
                        
                        logger.debug(f"{model_name} prediction: {predictions[model_name]} (confidence: {confidences[model_name]:.3f})")
                    
                except Exception as e:
                    logger.warning(f"Error getting prediction from {model_name}: {e}")
                    predictions[model_name] = 'HOLD'
                    confidences[model_name] = 0.0
            
            # Ensemble logic
            ensemble_signal, ensemble_confidence = self._combine_predictions(predictions, confidences)
            
            return {
                'signal': ensemble_signal,
                'confidence': ensemble_confidence,
                'individual_predictions': predictions,
                'individual_confidences': confidences
            }
            
        except Exception as e:
            logger.error(f"Error generating ensemble prediction: {e}")
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'individual_predictions': {},
                'individual_confidences': {}
            }
    
    def _combine_predictions(self, predictions: Dict[str, str], confidences: Dict[str, float]) -> Tuple[str, float]:
        """Combine predictions from multiple models"""
        if not predictions:
            return 'HOLD', 0.0
        
        # Weighted voting based on confidence
        signal_scores = {'BUY': 0.0, 'SELL': 0.0, 'HOLD': 0.0}
        total_weight = 0.0
        
        for model_name, signal in predictions.items():
            confidence = confidences.get(model_name, 0.0)
            signal_scores[signal] += confidence
            total_weight += confidence
        
        if total_weight == 0:
            return 'HOLD', 0.0
        
        # Normalize scores
        for signal in signal_scores:
            signal_scores[signal] /= total_weight
        
        # Get the signal with highest score
        best_signal = max(signal_scores, key=signal_scores.get)
        best_confidence = signal_scores[best_signal]
        
        # Require minimum confidence for non-HOLD signals
        min_confidence = self.config.trading.signals.min_confidence
        if best_signal != 'HOLD' and best_confidence < min_confidence:
            return 'HOLD', best_confidence
        
        return best_signal, best_confidence
    
    def _calculate_signal_levels(self, current_price: float, signal: str, atr: float) -> Dict[str, float]:
        """Calculate stop loss and take profit levels"""
        stop_loss_pips = self.config.trading.signals.stop_loss_pips
        take_profit_pips = self.config.trading.signals.take_profit_pips
        
        # Convert pips to price (for XAUUSD, 1 pip = 0.01)
        pip_value = 0.01
        stop_loss_distance = stop_loss_pips * pip_value
        take_profit_distance = take_profit_pips * pip_value
        
        # Use ATR for dynamic levels
        atr_multiplier = 2.0
        dynamic_stop = atr * atr_multiplier
        dynamic_tp = atr * atr_multiplier * self.config.trading.signals.risk_reward_ratio
        
        # Use the larger of fixed or dynamic levels
        stop_distance = max(stop_loss_distance, dynamic_stop)
        tp_distance = max(take_profit_distance, dynamic_tp)
        
        if signal == 'BUY':
            stop_loss = current_price - stop_distance
            take_profit = current_price + tp_distance
        elif signal == 'SELL':
            stop_loss = current_price + stop_distance
            take_profit = current_price - tp_distance
        else:
            stop_loss = current_price
            take_profit = current_price
        
        return {
            'entry_price': current_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit
        }
    
    def _should_generate_signal(self, signal: str) -> bool:
        """Check if signal should be generated based on rate limiting"""
        current_time = datetime.utcnow()
        current_date = current_time.date()
        
        # Reset daily counter if new day
        if current_date != self.last_reset_date:
            self.daily_signal_count = 0
            self.last_reset_date = current_date
        
        # Check daily limit
        if self.daily_signal_count >= self.config.trading.signals.max_signals_per_day:
            logger.info("Daily signal limit reached")
            return False
        
        # Check time since last signal of same type
        last_signal_time = self.last_signal_time.get(signal, datetime.min)
        time_since_last = current_time - last_signal_time
        
        # Minimum time between signals (15 minutes)
        min_interval = timedelta(minutes=15)
        if time_since_last < min_interval:
            logger.debug(f"Signal {signal} too soon after last signal")
            return False
        
        return True
    
    async def generate_signal(self) -> Optional[Dict[str, Any]]:
        """Generate a single trading signal"""
        try:
            # Get market data
            data_15m, data_1h, data_4h = await self.get_market_data()
            
            # Engineer features
            featured_data = self.feature_engineer.engineer_features(
                data_15m, data_1h if not data_1h.empty else None, data_4h if not data_4h.empty else None
            )
            
            if featured_data.empty:
                logger.warning("No featured data available for signal generation")
                return None
            
            # Generate ensemble prediction
            prediction = await self.generate_ensemble_prediction(featured_data)
            
            signal = prediction['signal']
            confidence = prediction['confidence']
            
            # Skip if HOLD signal
            if signal == 'HOLD':
                logger.debug("Model prediction: HOLD")
                return None
            
            # Check if signal should be generated
            if not self._should_generate_signal(signal):
                return None
            
            # Validate signal using multi-timeframe analysis
            validation_result = await self.validator.validate_signal(
                signal, data_15m, data_1h, data_4h, featured_data
            )
            
            if not validation_result['is_valid']:
                logger.info(f"Signal {signal} failed validation: {validation_result['reason']}")
                return None
            
            # Calculate signal levels
            current_price = featured_data['close'].iloc[-1]
            atr = featured_data['atr_14'].iloc[-1] if 'atr_14' in featured_data.columns else current_price * 0.01
            
            levels = self._calculate_signal_levels(current_price, signal, atr)
            
            # Create signal data
            signal_data = {
                'symbol': self.config.trading.symbol,
                'direction': signal,
                'confidence': confidence,
                'entry_price': levels['entry_price'],
                'stop_loss': levels['stop_loss'],
                'take_profit': levels['take_profit'],
                'h4_trend': validation_result.get('h4_trend', 'Unknown'),
                'h1_momentum': validation_result.get('h1_momentum', 'Unknown'),
                'm15_pattern': validation_result.get('m15_pattern', 'Unknown'),
                'model_used': self.config.ai_models.primary_model,
                'features': str(prediction['individual_predictions']),
                'timestamp': datetime.utcnow()
            }
            
            # Store signal in database
            signal_id = await self.db_manager.insert_signal(signal_data)
            signal_data['id'] = signal_id
            
            # Update tracking
            self.last_signal_time[signal] = datetime.utcnow()
            self.daily_signal_count += 1
            
            logger.info(f"Generated {signal} signal with confidence {confidence:.3f}")
            
            return signal_data
            
        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return None
    
    async def start_real_time_monitoring(self):
        """Start real-time signal monitoring loop"""
        logger.info("Starting real-time signal monitoring...")
        
        # Load models first
        await self.load_models()
        
        # Signal delivery components
        from ..delivery.telegram_bot import TelegramBot
        telegram_bot = TelegramBot(self.config) if self.config.telegram.enabled else None
        
        if telegram_bot:
            await telegram_bot.start()
        
        try:
            while True:
                try:
                    # Generate signal
                    signal = await self.generate_signal()
                    
                    if signal:
                        logger.info(f"New signal generated: {signal['direction']} at {signal['entry_price']}")
                        
                        # Send signal via Telegram
                        if telegram_bot:
                            await telegram_bot.send_signal(signal)
                        
                        # Log signal
                        logger.info(
                            f"SIGNAL: {signal['direction']} {signal['symbol']} "
                            f"@ {signal['entry_price']:.2f} "
                            f"(SL: {signal['stop_loss']:.2f}, TP: {signal['take_profit']:.2f}) "
                            f"Confidence: {signal['confidence']:.3f}"
                        )
                    
                    # Wait before next check (15 minutes for 15m timeframe)
                    await asyncio.sleep(900)  # 15 minutes
                    
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    await asyncio.sleep(60)  # Wait 1 minute before retrying
                    
        except KeyboardInterrupt:
            logger.info("Signal monitoring stopped by user")
        except Exception as e:
            logger.error(f"Fatal error in signal monitoring: {e}")
            raise
        finally:
            if telegram_bot:
                await telegram_bot.stop()
