{"cells": [{"cell_type": "markdown", "metadata": {"id": "forex-ai-colab-title"}, "source": ["# 🚀 Forex AI Model Training in Google Colab\n", "\n", "This notebook will train your Forex AI models using Google Colab's GPU acceleration.\n", "\n", "## Prerequisites\n", "1. ✅ Exported training data from your local system\n", "2. ✅ Uploaded data to Google Drive in `ForexAI_Training` folder\n", "3. ✅ Enabled GPU runtime (Runtime → Change runtime type → GPU)\n", "\n", "## Training Options\n", "- **Quick Training**: 30-60 minutes (testing)\n", "- **Full Training**: 2-4 hours (production)\n", "- **Advanced Training**: 4-8 hours (best performance)\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup-section"}, "source": ["## 📦 Step 1: Environment Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install-packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch>=2.0.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n", "!pip install transformers>=4.30.0 optuna>=3.2.0 ta>=0.10.2 loguru>=0.7.0\n", "!pip install scikit-learn xgboost lightgbm pandas numpy matplotlib seaborn plotly\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import-libraries"}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import json\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, TensorDataset\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "import optuna\n", "from google.colab import drive, files\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup-environment"}, "outputs": [], "source": ["# Mount Google Drive and setup environment\n", "drive.mount('/content/drive')\n", "\n", "# Setup paths\n", "DATA_PATH = '/content/drive/MyDrive/ForexAI_Training'\n", "OUTPUT_PATH = '/content/output'\n", "os.makedirs(OUTPUT_PATH, exist_ok=True)\n", "\n", "# Check GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "if torch.cuda.is_available():\n", "    print(f\"🚀 GPU available: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"🚀 GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "else:\n", "    print(\"⚠️ No GPU available - using CPU\")\n", "\n", "print(f\"✅ Environment setup completed! Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data-loading-section"}, "source": ["## 📊 Step 2: Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load-data"}, "outputs": [], "source": ["# Load training data\n", "print(\"📊 Loading training data...\")\n", "\n", "# Load main training data\n", "training_file = os.path.join(DATA_PATH, 'training_data_15m.csv')\n", "data = pd.read_csv(training_file)\n", "print(f\"✅ Loaded training data: {data.shape}\")\n", "\n", "# Load configuration\n", "config_file = os.path.join(DATA_PATH, 'model_config.json')\n", "if os.path.exists(config_file):\n", "    with open(config_file, 'r') as f:\n", "        config = json.load(f)\n", "    print(\"✅ Loaded model configuration\")\n", "else:\n", "    print(\"⚠️ Using default configuration\")\n", "    config = {\n", "        'ai_models': {\n", "            'training': {\n", "                'batch_size': 32,\n", "                'epochs': 50,\n", "                'learning_rate': 0.001,\n", "                'validation_split': 0.2\n", "            }\n", "        }\n", "    }\n", "\n", "# Display data info\n", "print(f\"\\n📈 Data Overview:\")\n", "print(f\"  Shape: {data.shape}\")\n", "print(f\"  Columns: {len(data.columns)}\")\n", "print(f\"  Memory usage: {data.memory_usage(deep=True).sum() / 1e6:.1f} MB\")\n", "print(f\"  Missing values: {data.isnull().sum().sum()}\")\n", "\n", "# Show first few rows\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "preprocess-data"}, "outputs": [], "source": ["# Data preprocessing\n", "print(\"🔧 Preprocessing data...\")\n", "\n", "# Remove non-numeric columns and handle missing values\n", "numeric_columns = data.select_dtypes(include=[np.number]).columns\n", "data_numeric = data[numeric_columns].copy()\n", "\n", "# Fill missing values\n", "data_numeric = data_numeric.fillna(method='ffill').fillna(0)\n", "\n", "# Create target variable (simplified - you may need to adjust based on your data)\n", "# This assumes you have a 'signal' or 'target' column, otherwise we'll create one\n", "if 'signal' in data_numeric.columns:\n", "    y = data_numeric['signal'].values\n", "    X = data_numeric.drop(['signal'], axis=1).values\n", "elif 'target' in data_numeric.columns:\n", "    y = data_numeric['target'].values\n", "    X = data_numeric.drop(['target'], axis=1).values\n", "else:\n", "    # Create simple target based on price movement (example)\n", "    if 'close' in data_numeric.columns:\n", "        price_change = data_numeric['close'].pct_change().fillna(0)\n", "        y = np.where(price_change > 0.001, 1, np.where(price_change < -0.001, 2, 0))  # Buy, <PERSON><PERSON>, Hold\n", "        X = data_numeric.values\n", "    else:\n", "        print(\"❌ No suitable target column found. Please check your data.\")\n", "        raise ValueError(\"No target variable found\")\n", "\n", "print(f\"✅ Features shape: {X.shape}\")\n", "print(f\"✅ Target shape: {y.shape}\")\n", "print(f\"✅ Target distribution: {np.bincount(y)}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "print(\"✅ Data preprocessing completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model-definition-section"}, "source": ["## 🧠 Step 3: Model Definitions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "define-models"}, "outputs": [], "source": ["# Define Transformer Model\n", "class ForexTransformer(nn.Module):\n", "    def __init__(self, input_features, d_model=256, nhead=8, num_layers=6, dropout=0.1, sequence_length=100):\n", "        super().__init__()\n", "        self.d_model = d_model\n", "        self.sequence_length = sequence_length\n", "        \n", "        # Input projection\n", "        self.input_projection = nn.Linear(input_features, d_model)\n", "        \n", "        # Positional encoding\n", "        self.pos_encoding = nn.Parameter(torch.randn(sequence_length, d_model))\n", "        \n", "        # Transformer encoder\n", "        encoder_layer = nn.TransformerEncoderLayer(\n", "            d_model=d_model, nhead=nhead, dropout=dropout, batch_first=True\n", "        )\n", "        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)\n", "        \n", "        # Output layers\n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(d_model, d_model // 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(d_model // 2, 3)  # 3 classes: BUY, SELL, HOLD\n", "        )\n", "        \n", "        self.confidence_head = nn.Sequential(\n", "            nn.Linear(d_model, d_model // 2),\n", "            nn.ReLU(),\n", "            nn.Linear(d_model // 2, 1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "    \n", "    def forward(self, x):\n", "        batch_size, seq_len, features = x.shape\n", "        \n", "        # Project input\n", "        x = self.input_projection(x)\n", "        \n", "        # Add positional encoding\n", "        x = x + self.pos_encoding[:seq_len].unsqueeze(0)\n", "        \n", "        # Apply transformer\n", "        encoded = self.transformer(x)\n", "        \n", "        # Pool and classify\n", "        pooled = encoded.mean(dim=1)\n", "        predictions = self.classifier(pooled)\n", "        confidence = self.confidence_head(pooled)\n", "        \n", "        return predictions, confidence.squeeze(-1)\n", "\n", "# Define CNN-LSTM Model\n", "class CNNLSTMModel(nn.Module):\n", "    def __init__(self, input_features, cnn_filters=[32, 64, 128], lstm_units=128, dropout=0.2):\n", "        super().__init__()\n", "        \n", "        # CNN layers\n", "        self.conv1 = nn.Conv1d(input_features, cnn_filters[0], kernel_size=3, padding=1)\n", "        self.conv2 = nn.Conv1d(cnn_filters[0], cnn_filters[1], kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv1d(cnn_filters[1], cnn_filters[2], kernel_size=3, padding=1)\n", "        \n", "        # LSTM layer\n", "        self.lstm = nn.LSTM(cnn_filters[2], lstm_units, batch_first=True, bidirectional=True)\n", "        \n", "        # Output layers\n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(lstm_units * 2, 64),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(64, 3)\n", "        )\n", "        \n", "        self.confidence_head = nn.Sequential(\n", "            nn.Linear(lstm_units * 2, 32),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(32, 1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "    \n", "    def forward(self, x):\n", "        # CNN processing\n", "        x = x.transpose(1, 2)  # (batch, features, seq_len)\n", "        x = torch.relu(self.conv1(x))\n", "        x = torch.relu(self.conv2(x))\n", "        x = torch.relu(self.conv3(x))\n", "        \n", "        # Back to (batch, seq_len, features)\n", "        x = x.transpose(1, 2)\n", "        \n", "        # LSTM processing\n", "        lstm_out, _ = self.lstm(x)\n", "        \n", "        # Use last output\n", "        final_output = lstm_out[:, -1, :]\n", "        \n", "        # Classify\n", "        predictions = self.classifier(final_output)\n", "        confidence = self.confidence_head(final_output)\n", "        \n", "        return predictions, confidence.squeeze(-1)\n", "\n", "print(\"✅ Model architectures defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "training-section"}, "source": ["## 🏋️ Step 4: Training Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training-config"}, "outputs": [], "source": ["# Training configuration\n", "TRAINING_MODE = \"quick\"  # Change to \"full\" or \"advanced\" for longer training\n", "\n", "training_configs = {\n", "    \"quick\": {\n", "        \"epochs\": 20,\n", "        \"batch_size\": 64,\n", "        \"learning_rate\": 0.001,\n", "        \"sequence_length\": 50\n", "    },\n", "    \"full\": {\n", "        \"epochs\": 100,\n", "        \"batch_size\": 32,\n", "        \"learning_rate\": 0.0005,\n", "        \"sequence_length\": 100\n", "    },\n", "    \"advanced\": {\n", "        \"epochs\": 200,\n", "        \"batch_size\": 16,\n", "        \"learning_rate\": 0.0001,\n", "        \"sequence_length\": 150\n", "    }\n", "}\n", "\n", "train_config = training_configs[TRAINING_MODE]\n", "print(f\"🎯 Training mode: {TRAINING_MODE.upper()}\")\n", "print(f\"📊 Configuration: {train_config}\")\n", "\n", "# Prepare sequence data\n", "def create_sequences(X, y, sequence_length):\n", "    sequences_X, sequences_y = [], []\n", "    \n", "    for i in range(len(X) - sequence_length + 1):\n", "        sequences_X.append(X[i:i + sequence_length])\n", "        sequences_y.append(y[i + sequence_length - 1])  # Use last label in sequence\n", "    \n", "    return np.array(sequences_X), np.array(sequences_y)\n", "\n", "# Create sequences\n", "print(\"🔄 Creating sequences...\")\n", "X_seq, y_seq = create_sequences(X_scaled, y, train_config['sequence_length'])\n", "print(f\"✅ Sequences created: {X_seq.shape}, {y_seq.shape}\")\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_seq, y_seq, test_size=0.2, random_state=42, stratify=y_seq\n", ")\n", "\n", "print(f\"✅ Data split completed:\")\n", "print(f\"  Train: {X_train.shape}, {y_train.shape}\")\n", "print(f\"  Test: {X_test.shape}, {y_test.shape}\")\n", "\n", "# Convert to PyTorch tensors\n", "X_train_tensor = torch.FloatTensor(X_train).to(device)\n", "y_train_tensor = torch.LongTensor(y_train).to(device)\n", "X_test_tensor = torch.FloatTensor(X_test).to(device)\n", "y_test_tensor = torch.LongTensor(y_test).to(device)\n", "\n", "# Create data loaders\n", "train_dataset = TensorDataset(X_train_tensor, y_train_tensor)\n", "test_dataset = TensorDataset(X_test_tensor, y_test_tensor)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=train_config['batch_size'], shuffle=True)\n", "test_loader = DataLoader(test_dataset, batch_size=train_config['batch_size'], shuffle=False)\n", "\n", "print(\"✅ Data loaders created!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model-training-section"}, "source": ["## 🚀 Step 5: Model Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training-functions"}, "outputs": [], "source": ["# Training functions\n", "def train_epoch(model, train_loader, optimizer, criterion, device):\n", "    model.train()\n", "    total_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    for batch_idx, (data, targets) in enumerate(tqdm(train_loader, desc=\"Training\")):\n", "        data, targets = data.to(device), targets.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        predictions, confidence = model(data)\n", "        loss = criterion(predictions, targets)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        total_loss += loss.item()\n", "        _, predicted = predictions.max(1)\n", "        total += targets.size(0)\n", "        correct += predicted.eq(targets).sum().item()\n", "    \n", "    return total_loss / len(train_loader), 100. * correct / total\n", "\n", "def evaluate_model(model, test_loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    all_predictions = []\n", "    all_targets = []\n", "    \n", "    with torch.no_grad():\n", "        for data, targets in tqdm(test_loader, desc=\"Evaluating\"):\n", "            data, targets = data.to(device), targets.to(device)\n", "            \n", "            predictions, confidence = model(data)\n", "            loss = criterion(predictions, targets)\n", "            \n", "            total_loss += loss.item()\n", "            _, predicted = predictions.max(1)\n", "            total += targets.size(0)\n", "            correct += predicted.eq(targets).sum().item()\n", "            \n", "            all_predictions.extend(predicted.cpu().numpy())\n", "            all_targets.extend(targets.cpu().numpy())\n", "    \n", "    accuracy = 100. * correct / total\n", "    f1 = f1_score(all_targets, all_predictions, average='weighted')\n", "    \n", "    return {\n", "        'loss': total_loss / len(test_loader),\n", "        'accuracy': accuracy,\n", "        'f1_score': f1\n", "    }\n", "\n", "def train_model(model, model_name, train_loader, test_loader, config, device):\n", "    print(f\"\\n🚀 Training {model_name}...\")\n", "    \n", "    # Setup training\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n", "    \n", "    # Training history\n", "    history = []\n", "    best_f1 = 0.0\n", "    \n", "    for epoch in range(config['epochs']):\n", "        # Train\n", "        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)\n", "        \n", "        # Evaluate\n", "        val_metrics = evaluate_model(model, test_loader, criterion, device)\n", "        \n", "        # Update learning rate\n", "        scheduler.step(val_metrics['loss'])\n", "        \n", "        # Log progress\n", "        print(f\"Epoch {epoch+1}/{config['epochs']} - \"\n", "              f\"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}% - \"\n", "              f\"Val Loss: {val_metrics['loss']:.4f}, Val Acc: {val_metrics['accuracy']:.2f}%, \"\n", "              f\"Val F1: {val_metrics['f1_score']:.4f}\")\n", "        \n", "        # Save history\n", "        history.append({\n", "            'epoch': epoch + 1,\n", "            'train_loss': train_loss,\n", "            'train_accuracy': train_acc,\n", "            'val_loss': val_metrics['loss'],\n", "            'val_accuracy': val_metrics['accuracy'],\n", "            'val_f1': val_metrics['f1_score'],\n", "            'learning_rate': optimizer.param_groups[0]['lr']\n", "        })\n", "        \n", "        # Save best model\n", "        if val_metrics['f1_score'] > best_f1:\n", "            best_f1 = val_metrics['f1_score']\n", "            torch.save(model.state_dict(), f'{OUTPUT_PATH}/{model_name}_best.pth')\n", "    \n", "    print(f\"✅ {model_name} training completed! Best F1: {best_f1:.4f}\")\n", "    return history, val_metrics\n", "\n", "print(\"✅ Training functions defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train-transformer"}, "outputs": [], "source": ["# Train Transformer Model\n", "print(\"🤖 Initializing Transformer Model...\")\n", "\n", "input_features = X_train.shape[2]\n", "transformer_model = ForexTransformer(\n", "    input_features=input_features,\n", "    d_model=256,\n", "    nhead=8,\n", "    num_layers=6,\n", "    dropout=0.1,\n", "    sequence_length=train_config['sequence_length']\n", ").to(device)\n", "\n", "print(f\"✅ Transformer model created with {sum(p.numel() for p in transformer_model.parameters())} parameters\")\n", "\n", "# Train the model\n", "transformer_history, transformer_metrics = train_model(\n", "    transformer_model, \"transformer\", train_loader, test_loader, train_config, device\n", ")\n", "\n", "print(f\"🎉 Transformer training completed!\")\n", "print(f\"Final metrics: {transformer_metrics}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train-cnn-lstm"}, "outputs": [], "source": ["# Train CNN-LSTM Model\n", "print(\"🤖 Initializing CNN-LSTM Model...\")\n", "\n", "cnn_lstm_model = CNNLSTMModel(\n", "    input_features=input_features,\n", "    cnn_filters=[32, 64, 128],\n", "    lstm_units=128,\n", "    dropout=0.2\n", ").to(device)\n", "\n", "print(f\"✅ CNN-LSTM model created with {sum(p.numel() for p in cnn_lstm_model.parameters())} parameters\")\n", "\n", "# Train the model\n", "cnn_lstm_history, cnn_lstm_metrics = train_model(\n", "    cnn_lstm_model, \"cnn_lstm\", train_loader, test_loader, train_config, device\n", ")\n", "\n", "print(f\"🎉 CNN-LSTM training completed!\")\n", "print(f\"Final metrics: {cnn_lstm_metrics}\")"]}, {"cell_type": "markdown", "metadata": {"id": "visualization-section"}, "source": ["## 📊 Step 6: Training Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot-results"}, "outputs": [], "source": ["# Plot training results\n", "def plot_training_history(history, model_name):\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle(f'Training History - {model_name}', fontsize=16)\n", "    \n", "    epochs = [h['epoch'] for h in history]\n", "    \n", "    # Plot loss\n", "    axes[0, 0].plot(epochs, [h['train_loss'] for h in history], label='Train Loss', color='blue')\n", "    axes[0, 0].plot(epochs, [h['val_loss'] for h in history], label='Validation Loss', color='red')\n", "    axes[0, 0].set_title('Loss')\n", "    axes[0, 0].set_xlabel('Epoch')\n", "    axes[0, 0].set_ylabel('Loss')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True)\n", "    \n", "    # Plot accuracy\n", "    axes[0, 1].plot(epochs, [h['train_accuracy'] for h in history], label='Train Accuracy', color='blue')\n", "    axes[0, 1].plot(epochs, [h['val_accuracy'] for h in history], label='Validation Accuracy', color='red')\n", "    axes[0, 1].set_title('Accuracy')\n", "    axes[0, 1].set_xlabel('Epoch')\n", "    axes[0, 1].set_ylabel('Accuracy (%)')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True)\n", "    \n", "    # Plot F1 score\n", "    axes[1, 0].plot(epochs, [h['val_f1'] for h in history], label='Validation F1', color='green')\n", "    axes[1, 0].set_title('F1 Score')\n", "    axes[1, 0].set_xlabel('Epoch')\n", "    axes[1, 0].set_ylabel('F1 Score')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True)\n", "    \n", "    # Plot learning rate\n", "    axes[1, 1].plot(epochs, [h['learning_rate'] for h in history], label='Learning Rate', color='purple')\n", "    axes[1, 1].set_title('Learning Rate')\n", "    axes[1, 1].set_xlabel('Epoch')\n", "    axes[1, 1].set_ylabel('Learning Rate')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True)\n", "    axes[1, 1].set_yscale('log')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(f'{OUTPUT_PATH}/{model_name}_training_history.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# Plot results for both models\n", "plot_training_history(transformer_history, 'Transformer')\n", "plot_training_history(cnn_lstm_history, 'CNN-LSTM')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "model-comparison"}, "outputs": [], "source": ["# Model comparison\n", "comparison_data = {\n", "    'Model': ['Transformer', 'CNN-LSTM'],\n", "    'Accuracy (%)': [transformer_metrics['accuracy'], cnn_lstm_metrics['accuracy']],\n", "    'F1 Score': [transformer_metrics['f1_score'], cnn_lstm_metrics['f1_score']],\n", "    'Final Loss': [transformer_metrics['loss'], cnn_lstm_metrics['loss']]\n", "}\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "print(\"📊 Model Comparison:\")\n", "print(comparison_df.to_string(index=False))\n", "\n", "# Plot comparison\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "\n", "# Accuracy comparison\n", "axes[0].bar(comparison_df['Model'], comparison_df['Accuracy (%)'], color=['blue', 'orange'])\n", "axes[0].set_title('Model Accuracy Comparison')\n", "axes[0].set_ylabel('Accuracy (%)')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# F1 Score comparison\n", "axes[1].bar(comparison_df['Model'], comparison_df['F1 Score'], color=['green', 'red'])\n", "axes[1].set_title('Model F1 Score Comparison')\n", "axes[1].set_ylabel('F1 Score')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# Loss comparison\n", "axes[2].bar(comparison_df['Model'], comparison_df['Final Loss'], color=['purple', 'brown'])\n", "axes[2].set_title('Model Loss Comparison')\n", "axes[2].set_ylabel('Loss')\n", "axes[2].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig(f'{OUTPUT_PATH}/model_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "# Determine best model\n", "best_model_name = 'Transformer' if transformer_metrics['f1_score'] > cnn_lstm_metrics['f1_score'] else 'CNN-LSTM'\n", "print(f\"\\n🏆 Best performing model: {best_model_name}\")"]}, {"cell_type": "markdown", "metadata": {"id": "save-models-section"}, "source": ["## 💾 Step 7: Save and Download Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save-models"}, "outputs": [], "source": ["# Save models with metadata\n", "import json\n", "import zipfile\n", "from datetime import datetime\n", "\n", "def save_model_with_metadata(model, model_name, metrics, history, config):\n", "    # Create model directory\n", "    model_dir = f'{OUTPUT_PATH}/{model_name}_final'\n", "    os.makedirs(model_dir, exist_ok=True)\n", "    \n", "    # Save model state dict\n", "    torch.save(model.state_dict(), f'{model_dir}/{model_name}.pth')\n", "    \n", "    # Save model metadata\n", "    metadata = {\n", "        'model_name': model_name,\n", "        'model_type': type(model).__name__,\n", "        'training_date': datetime.now().isoformat(),\n", "        'training_config': config,\n", "        'final_metrics': metrics,\n", "        'model_parameters': sum(p.numel() for p in model.parameters()),\n", "        'trainable_parameters': sum(p.numel() for p in model.parameters() if p.requires_grad),\n", "        'input_features': input_features,\n", "        'sequence_length': config['sequence_length'],\n", "        'device_used': str(device)\n", "    }\n", "    \n", "    with open(f'{model_dir}/{model_name}_metadata.json', 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    \n", "    # Save training history\n", "    with open(f'{model_dir}/{model_name}_history.json', 'w') as f:\n", "        json.dump(history, f, indent=2)\n", "    \n", "    # Save scaler for preprocessing\n", "    import joblib\n", "    joblib.dump(scaler, f'{model_dir}/{model_name}_scaler.pkl')\n", "    \n", "    print(f\"✅ {model_name} saved with metadata to {model_dir}\")\n", "    \n", "    # Also save to Google Drive\n", "    drive_dir = f'/content/drive/MyDrive/ForexAI_Training/trained_models/{model_name}_final'\n", "    os.makedirs(drive_dir, exist_ok=True)\n", "    \n", "    # Copy files to Drive\n", "    import shutil\n", "    for file in os.listdir(model_dir):\n", "        shutil.copy2(f'{model_dir}/{file}', f'{drive_dir}/{file}')\n", "    \n", "    print(f\"✅ {model_name} backed up to Google Drive\")\n", "\n", "# Save both models\n", "save_model_with_metadata(transformer_model, 'transformer', transformer_metrics, transformer_history, train_config)\n", "save_model_with_metadata(cnn_lstm_model, 'cnn_lstm', cnn_lstm_metrics, cnn_lstm_history, train_config)\n", "\n", "print(\"\\n🎉 All models saved successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "download-models"}, "outputs": [], "source": ["# Create downloadable zip file\n", "zip_filename = '/content/forex_ai_trained_models.zip'\n", "\n", "with zipfile.ZipFile(zip_filename, 'w') as zipf:\n", "    # Add all model files\n", "    for root, dirs, files in os.walk(OUTPUT_PATH):\n", "        for file in files:\n", "            file_path = os.path.join(root, file)\n", "            arcname = os.path.relpath(file_path, OUTPUT_PATH)\n", "            zipf.write(file_path, arcname)\n", "\n", "    # Add training summary\n", "    summary = {\n", "        'training_completed': datetime.now().isoformat(),\n", "        'training_mode': TRAINING_MODE,\n", "        'models_trained': ['transformer', 'cnn_lstm'],\n", "        'best_model': best_model_name.lower(),\n", "        'transformer_metrics': transformer_metrics,\n", "        'cnn_lstm_metrics': cnn_lstm_metrics,\n", "        'training_config': train_config,\n", "        'data_shape': {\n", "            'original': list(X.shape),\n", "            'sequences': list(X_seq.shape)\n", "        }\n", "    }\n", "\n", "    summary_file = '/content/training_summary.json'\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    zipf.write(summary_file, 'training_summary.json')\n", "\n", "print(f\"📦 Created downloadable package: {zip_filename}\")\n", "print(f\"📊 Package size: {os.path.getsize(zip_filename) / 1e6:.1f} MB\")\n", "\n", "# Download the zip file\n", "# Ensure google.colab.files is correctly imported and not overwritten\n", "from google.colab import files\n", "files.download(zip_filename)\n", "\n", "print(\"\\n🎉 TRAINING COMPLETED SUCCESSFULLY! 🎉\")\n", "print(\"\\n📋 Summary:\")\n", "print(f\"  • Training mode: {TRAINING_MODE.upper()}\")\n", "print(f\"  • Models trained: Transformer, CNN-LSTM\")\n", "print(f\"  • Best model: {best_model_name}\")\n", "print(f\"  • Transformer F1: {transformer_metrics['f1_score']:.4f}\")\n", "print(f\"  • CNN-LSTM F1: {cnn_lstm_metrics['f1_score']:.4f}\")\n", "print(f\"  • Models saved to Google Drive and downloaded\")\n", "print(\"\\n📥 Next steps:\")\n", "print(\"  1. Extract the downloaded zip file\")\n", "print(\"  2. Copy model files to your local 'models/saved_models/' directory\")\n", "print(\"  3. Update your config.yaml with new model paths\")\n", "print(\"  4. Test the models in your local environment\")\n", "print(\"\\n🚀 Happy trading with your new AI models!\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}