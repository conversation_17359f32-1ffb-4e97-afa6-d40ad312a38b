# Create downloadable zip file
zip_filename = '/content/forex_ai_trained_models.zip'

with zipfile.ZipFile(zip_filename, 'w') as zipf:
    # Add all model files
    for root, dirs, files in os.walk(OUTPUT_PATH):
        for file in files:
            file_path = os.path.join(root, file)
            arcname = os.path.relpath(file_path, OUTPUT_PATH)
            zipf.write(file_path, arcname)

    # Add training summary
    summary = {
        'training_completed': datetime.now().isoformat(),
        'training_mode': TRAINING_MODE,
        'models_trained': ['transformer', 'cnn_lstm'],
        'best_model': best_model_name.lower(),
        'transformer_metrics': transformer_metrics,
        'cnn_lstm_metrics': cnn_lstm_metrics,
        'training_config': train_config,
        'data_shape': {
            'original': list(X.shape),
            'sequences': list(X_seq.shape)
        }
    }

    summary_file = '/content/training_summary.json'
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    zipf.write(summary_file, 'training_summary.json')

print(f"📦 Created downloadable package: {zip_filename}")
print(f"📊 Package size: {os.path.getsize(zip_filename) / 1e6:.1f} MB")

# Download the zip file
# Ensure google.colab.files is correctly imported and not overwritten
from google.colab import files
files.download(zip_filename)

print("\n🎉 TRAINING COMPLETED SUCCESSFULLY! 🎉")
print("\n📋 Summary:")
print(f"  • Training mode: {TRAINING_MODE.upper()}")
print(f"  • Models trained: Transformer, CNN-LSTM")
print(f"  • Best model: {best_model_name}")
print(f"  • Transformer F1: {transformer_metrics['f1_score']:.4f}")
print(f"  • CNN-LSTM F1: {cnn_lstm_metrics['f1_score']:.4f}")
print(f"  • Models saved to Google Drive and downloaded")
print("\n📥 Next steps:")
print("  1. Extract the downloaded zip file")
print("  2. Copy model files to your local 'models/saved_models/' directory")
print("  3. Update your config.yaml with new model paths")
print("  4. Test the models in your local environment")
print("\n🚀 Happy trading with your new AI models!")