"""
FastAPI web service for Forex AI System
Provides REST API and web dashboard
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTM<PERSON>esponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional
import pandas as pd
from pathlib import Path

from ..utils.config import Config
from ..utils.database import DatabaseManager
from ..data.collectors.data_collector import DataCollector
from ..signals.generators.signal_generator import SignalGenerator


def create_app(config: Config) -> FastAPI:
    """Create FastAPI application"""
    
    app = FastAPI(
        title="Forex AI Signal System",
        description="Real-time AI-powered forex signal generation for XAUUSD",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Initialize components
    db_manager = DatabaseManager(config)
    data_collector = DataCollector(config)
    signal_generator = SignalGenerator(config)
    
    # Setup templates and static files
    templates_dir = Path(__file__).parent / "templates"
    static_dir = Path(__file__).parent / "static"
    
    templates_dir.mkdir(exist_ok=True)
    static_dir.mkdir(exist_ok=True)
    
    templates = Jinja2Templates(directory=str(templates_dir))
    
    if static_dir.exists():
        app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    # API Routes
    
    @app.get("/")
    async def dashboard():
        """Main dashboard page"""
        return HTMLResponse("""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Forex AI Signal System</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .signal { padding: 15px; border-left: 4px solid #3498db; background: #ecf0f1; margin: 10px 0; }
                .signal.buy { border-left-color: #27ae60; }
                .signal.sell { border-left-color: #e74c3c; }
                .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
                .btn:hover { background: #2980b9; }
                .status { display: inline-block; padding: 5px 10px; border-radius: 20px; font-size: 12px; }
                .status.online { background: #27ae60; color: white; }
                .status.offline { background: #e74c3c; color: white; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🤖 Forex AI Signal System</h1>
                    <p>Real-time AI-powered XAUUSD signal generation</p>
                    <span class="status online">● ONLINE</span>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <h3>📊 System Status</h3>
                        <p><strong>Symbol:</strong> XAUUSD</p>
                        <p><strong>Timeframes:</strong> 15M, 1H, 4H</p>
                        <p><strong>AI Models:</strong> Transformer, CNN-LSTM</p>
                        <p><strong>Last Update:</strong> <span id="lastUpdate">Loading...</span></p>
                        <button class="btn" onclick="refreshData()">Refresh</button>
                    </div>
                    
                    <div class="card">
                        <h3>📈 Recent Signals</h3>
                        <div id="signals">Loading signals...</div>
                        <button class="btn" onclick="loadSignals()">Load More</button>
                    </div>
                    
                    <div class="card">
                        <h3>📊 Performance</h3>
                        <div id="performance">Loading performance data...</div>
                    </div>
                    
                    <div class="card">
                        <h3>🎯 Quick Actions</h3>
                        <button class="btn" onclick="generateSignal()">Generate Signal</button>
                        <button class="btn" onclick="viewStats()">View Statistics</button>
                        <button class="btn" onclick="downloadData()">Download Data</button>
                    </div>
                </div>
            </div>
            
            <script>
                async function refreshData() {
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
                    await loadSignals();
                    await loadPerformance();
                }
                
                async function loadSignals() {
                    try {
                        const response = await fetch('/api/signals/recent');
                        const signals = await response.json();
                        
                        const signalsDiv = document.getElementById('signals');
                        if (signals.length === 0) {
                            signalsDiv.innerHTML = '<p>No recent signals</p>';
                            return;
                        }
                        
                        signalsDiv.innerHTML = signals.map(signal => `
                            <div class="signal ${signal.direction.toLowerCase()}">
                                <strong>${signal.direction}</strong> @ ${signal.entry_price}<br>
                                SL: ${signal.stop_loss} | TP: ${signal.take_profit}<br>
                                Confidence: ${(signal.confidence * 100).toFixed(1)}%<br>
                                <small>${new Date(signal.timestamp).toLocaleString()}</small>
                            </div>
                        `).join('');
                    } catch (error) {
                        document.getElementById('signals').innerHTML = '<p>Error loading signals</p>';
                    }
                }
                
                async function loadPerformance() {
                    try {
                        const response = await fetch('/api/performance/stats');
                        const stats = await response.json();
                        
                        document.getElementById('performance').innerHTML = `
                            <p><strong>Total Trades:</strong> ${stats.total_trades}</p>
                            <p><strong>Win Rate:</strong> ${(stats.win_rate * 100).toFixed(1)}%</p>
                            <p><strong>Total P&L:</strong> ${stats.total_pnl.toFixed(2)} pips</p>
                            <p><strong>Profit Factor:</strong> ${stats.profit_factor.toFixed(2)}</p>
                        `;
                    } catch (error) {
                        document.getElementById('performance').innerHTML = '<p>Error loading performance data</p>';
                    }
                }
                
                async function generateSignal() {
                    alert('Manual signal generation triggered');
                }
                
                function viewStats() {
                    window.open('/api/performance/detailed', '_blank');
                }
                
                function downloadData() {
                    window.open('/api/data/export', '_blank');
                }
                
                // Auto-refresh every 5 minutes
                setInterval(refreshData, 300000);
                
                // Initial load
                refreshData();
            </script>
        </body>
        </html>
        """)
    
    @app.get("/api/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
    
    @app.get("/api/signals/recent")
    async def get_recent_signals(limit: int = 10):
        """Get recent signals"""
        try:
            signals = await db_manager.get_active_signals()
            return signals[-limit:] if signals else []
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/signals/active")
    async def get_active_signals():
        """Get currently active signals"""
        try:
            signals = await db_manager.get_active_signals()
            return signals
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/signals/generate")
    async def generate_signal_manually(background_tasks: BackgroundTasks):
        """Manually trigger signal generation"""
        try:
            # Add signal generation to background tasks
            background_tasks.add_task(signal_generator.generate_signal)
            return {"message": "Signal generation triggered"}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/performance/stats")
    async def get_performance_stats(days: int = 30):
        """Get performance statistics"""
        try:
            stats = await db_manager.get_trade_statistics(days)
            return stats
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/performance/detailed")
    async def get_detailed_performance():
        """Get detailed performance metrics"""
        try:
            stats_7d = await db_manager.get_trade_statistics(7)
            stats_30d = await db_manager.get_trade_statistics(30)
            stats_90d = await db_manager.get_trade_statistics(90)
            
            return {
                "7_days": stats_7d,
                "30_days": stats_30d,
                "90_days": stats_90d,
                "generated_at": datetime.utcnow().isoformat()
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/data/market")
    async def get_market_data(timeframe: str = "15m", limit: int = 100):
        """Get market data"""
        try:
            data = await data_collector.get_latest_data(
                config.trading.symbol, timeframe, limit
            )
            
            if data.empty:
                return {"data": [], "message": "No data available"}
            
            # Convert to JSON-serializable format
            data_dict = data.to_dict('records')
            
            return {
                "symbol": config.trading.symbol,
                "timeframe": timeframe,
                "data": data_dict,
                "count": len(data_dict)
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/data/export")
    async def export_data(format: str = "csv", days: int = 30):
        """Export historical data"""
        try:
            # Get data from the last N days
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            data = await db_manager.get_historical_data(
                config.trading.symbol, "15m"
            )
            
            if data.empty:
                raise HTTPException(status_code=404, detail="No data available")
            
            # Filter by date range
            data = data[data['timestamp'] >= start_date]
            
            if format.lower() == "csv":
                csv_data = data.to_csv(index=False)
                return JSONResponse(
                    content={"data": csv_data, "filename": f"xauusd_data_{days}d.csv"},
                    headers={"Content-Type": "application/json"}
                )
            else:
                return data.to_dict('records')
                
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/config")
    async def get_config():
        """Get system configuration (public parts only)"""
        return {
            "trading": {
                "symbol": config.trading.symbol,
                "timeframes": config.trading.timeframes,
                "signals": {
                    "min_confidence": config.trading.signals.min_confidence,
                    "max_signals_per_day": config.trading.signals.max_signals_per_day,
                    "risk_reward_ratio": config.trading.signals.risk_reward_ratio
                }
            },
            "ai_models": {
                "primary_model": config.ai_models.primary_model,
                "ensemble_models": config.ai_models.ensemble_models
            },
            "features": {
                "technical_indicators": len(config.features.technical_indicators),
                "candlestick_patterns": len(config.features.candlestick_patterns)
            }
        }
    
    @app.get("/api/system/status")
    async def get_system_status():
        """Get detailed system status"""
        try:
            # Check various system components
            current_time = datetime.utcnow()
            
            # Check database connectivity
            try:
                await db_manager.get_trade_statistics(1)
                db_status = "connected"
            except:
                db_status = "error"
            
            # Check recent data availability
            try:
                data = await data_collector.get_latest_data(config.trading.symbol, "15m", 1)
                data_status = "available" if not data.empty else "no_data"
                last_data_time = data['timestamp'].iloc[-1] if not data.empty else None
            except:
                data_status = "error"
                last_data_time = None
            
            return {
                "timestamp": current_time.isoformat(),
                "uptime": "unknown",  # Would need to track startup time
                "database": {
                    "status": db_status,
                    "type": config.database.type
                },
                "data_feed": {
                    "status": data_status,
                    "last_update": last_data_time.isoformat() if last_data_time else None,
                    "primary_source": config.data_sources.primary
                },
                "ai_models": {
                    "loaded": len(signal_generator.models) if hasattr(signal_generator, 'models') else 0,
                    "configured": len(config.ai_models.ensemble_models)
                },
                "telegram": {
                    "enabled": config.telegram.enabled,
                    "configured": bool(config.telegram.bot_token)
                }
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    return app


# For running the app directly
if __name__ == "__main__":
    from ..utils.config import Config
    
    config = Config()
    app = create_app(config)
    
    uvicorn.run(
        app,
        host=config.dashboard.host,
        port=config.dashboard.port,
        log_level="info"
    )
